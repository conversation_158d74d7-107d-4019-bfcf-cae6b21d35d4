/**
 * Email Intelligence Service - AI-Powered Email Processing
 * Implements comprehensive email analysis using Bielik V3 AI
 * Part of FAZA 1: Komunikacja & Przetwarzanie Danych
 */

import { prisma } from '~/db.server';
import { bielikService } from './bielik.server';
import { sendCustomerCommunication } from './communication.service';
import { createServiceOrder } from './service-order.service';
import { createLead } from './customer.service';

export interface EmailMessage {
  id: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  timestamp: Date;
  attachments?: EmailAttachment[];
  headers?: Record<string, string>;
}

export interface EmailAttachment {
  name: string;
  contentType: string;
  size: number;
  data: Buffer;
}

export interface EmailAnalysis {
  category: 'INQUIRY' | 'COMPLAINT' | 'SERVICE_REQUEST' | 'QUOTE_REQUEST' | 'FOLLOW_UP' | 'SPAM' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE';
  confidence: number;
  extractedData: {
    customerName?: string;
    phoneNumber?: string;
    address?: string;
    serviceType?: string;
    urgency?: string;
    equipmentType?: string;
    problemDescription?: string;
  };
  suggestedActions: string[];
  autoResponse?: string;
}

export interface EmailProcessingResult {
  emailId: string;
  analysis: EmailAnalysis;
  customerId?: string;
  serviceOrderId?: string;
  communicationId: string;
  autoActionsPerformed: string[];
  requiresManualReview: boolean;
}

/**
 * Main Email Intelligence Service
 */
class EmailIntelligenceService {
  private imapConfig: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
  };

  constructor() {
    this.imapConfig = {
      host: process.env.EMAIL_IMAP_HOST || 'imap.gmail.com',
      port: parseInt(process.env.EMAIL_IMAP_PORT || '993'),
      secure: true,
      auth: {
        user: process.env.EMAIL_IMAP_USER || '',
        pass: process.env.EMAIL_IMAP_PASS || '',
      },
    };
  }

  /**
   * Process incoming email with AI analysis
   */
  async processIncomingEmail(email: EmailMessage): Promise<EmailProcessingResult> {
    try {
      console.log(`Processing email: ${email.subject} from ${email.from}`);

      // 1. Analyze email with AI
      const analysis = await this.analyzeEmailWithAI(email);

      // 2. Find or create customer
      const customerId = await this.findOrCreateCustomer(email, analysis);

      // 3. Create communication record
      const communicationId = await this.createCommunicationRecord(email, analysis, customerId);

      // 4. Perform automatic actions based on analysis
      const autoActions = await this.performAutomaticActions(email, analysis, customerId);

      // 5. Determine if manual review is needed
      const requiresManualReview = this.requiresManualReview(analysis);

      return {
        emailId: email.id,
        analysis,
        customerId,
        serviceOrderId: autoActions.serviceOrderId,
        communicationId,
        autoActionsPerformed: autoActions.actions,
        requiresManualReview,
      };
    } catch (error) {
      console.error('Email processing error:', error);
      throw new Error(`Failed to process email: ${error.message}`);
    }
  }

  /**
   * Analyze email content using Bielik V3 AI
   */
  private async analyzeEmailWithAI(email: EmailMessage): Promise<EmailAnalysis> {
    try {
      const prompt = this.buildEmailAnalysisPrompt(email);

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
        maxTokens: 1500,
      });

      return this.parseAIAnalysisResponse(response.content);
    } catch (error) {
      console.error('AI analysis error:', error);
      // Fallback to basic analysis
      return this.performBasicAnalysis(email);
    }
  }

  /**
   * Build comprehensive prompt for email analysis
   */
  private buildEmailAnalysisPrompt(email: EmailMessage): string {
    return `
Analyze this HVAC business email and provide structured analysis:

FROM: ${email.from}
TO: ${email.to}
SUBJECT: ${email.subject}
TIMESTAMP: ${email.timestamp.toISOString()}

EMAIL CONTENT:
${email.body}

Please analyze and respond with JSON format:
{
  "category": "INQUIRY|COMPLAINT|SERVICE_REQUEST|QUOTE_REQUEST|FOLLOW_UP|SPAM|OTHER",
  "priority": "LOW|MEDIUM|HIGH|URGENT",
  "sentiment": "POSITIVE|NEUTRAL|NEGATIVE",
  "confidence": 0.0-1.0,
  "extractedData": {
    "customerName": "extracted name or null",
    "phoneNumber": "extracted phone or null",
    "address": "extracted address or null",
    "serviceType": "installation|repair|maintenance|inspection|other",
    "urgency": "immediate|within_week|within_month|flexible",
    "equipmentType": "air_conditioning|heating|ventilation|heat_pump|other",
    "problemDescription": "brief description of the issue"
  },
  "suggestedActions": ["action1", "action2", "action3"],
  "autoResponse": "suggested automatic response text or null"
}

Focus on HVAC-specific terminology and context. Consider seasonal patterns and urgency indicators.
    `.trim();
  }

  /**
   * Parse AI response into structured analysis
   */
  private parseAIAnalysisResponse(content: string): EmailAnalysis {
    try {
      // Extract JSON from AI response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        category: parsed.category || 'OTHER',
        priority: parsed.priority || 'MEDIUM',
        sentiment: parsed.sentiment || 'NEUTRAL',
        confidence: parsed.confidence || 0.5,
        extractedData: parsed.extractedData || {},
        suggestedActions: parsed.suggestedActions || [],
        autoResponse: parsed.autoResponse || null,
      };
    } catch (error) {
      console.error('Failed to parse AI response:', error);
      // Return basic analysis as fallback
      return {
        category: 'OTHER',
        priority: 'MEDIUM',
        sentiment: 'NEUTRAL',
        confidence: 0.3,
        extractedData: {},
        suggestedActions: ['Manual review required'],
        autoResponse: null,
      };
    }
  }

  /**
   * Perform basic analysis without AI (fallback)
   */
  private performBasicAnalysis(email: EmailMessage): EmailAnalysis {
    const subject = email.subject.toLowerCase();
    const body = email.body.toLowerCase();

    // Basic keyword detection
    let category: EmailAnalysis['category'] = 'OTHER';
    let priority: EmailAnalysis['priority'] = 'MEDIUM';

    if (subject.includes('urgent') || body.includes('emergency')) {
      priority = 'URGENT';
    }

    if (subject.includes('quote') || body.includes('estimate')) {
      category = 'QUOTE_REQUEST';
    } else if (subject.includes('complaint') || body.includes('problem')) {
      category = 'COMPLAINT';
      priority = 'HIGH';
    } else if (subject.includes('service') || body.includes('repair')) {
      category = 'SERVICE_REQUEST';
    }

    return {
      category,
      priority,
      sentiment: 'NEUTRAL',
      confidence: 0.4,
      extractedData: {},
      suggestedActions: ['Manual review and categorization needed'],
      autoResponse: null,
    };
  }

  /**
   * Find existing customer or create new lead
   */
  private async findOrCreateCustomer(email: EmailMessage, analysis: EmailAnalysis): Promise<string> {
    try {
      // Try to find existing customer by email
      let customer = await prisma.customer.findFirst({
        where: {
          email: email.from,
        },
      });

      if (customer) {
        return customer.id;
      }

      // Try to find by extracted name and phone
      if (analysis.extractedData.customerName && analysis.extractedData.phoneNumber) {
        customer = await prisma.customer.findFirst({
          where: {
            AND: [
              { name: { contains: analysis.extractedData.customerName, mode: 'insensitive' } },
              { phone: analysis.extractedData.phoneNumber },
            ],
          },
        });

        if (customer) {
          // Update email if not set
          if (!customer.email) {
            await prisma.customer.update({
              where: { id: customer.id },
              data: { email: email.from },
            });
          }
          return customer.id;
        }
      }

      // Create new customer/lead
      const newCustomer = await prisma.customer.create({
        data: {
          name: analysis.extractedData.customerName || this.extractNameFromEmail(email.from),
          email: email.from,
          phone: analysis.extractedData.phoneNumber || null,
          address: analysis.extractedData.address || null,
          status: 'LEAD',
          source: 'EMAIL',
          userId: await this.getDefaultUserId(), // You'll need to implement this
          notes: `Created from email: ${email.subject}`,
        },
      });

      return newCustomer.id;
    } catch (error) {
      console.error('Error finding/creating customer:', error);
      throw new Error('Failed to process customer data');
    }
  }

  /**
   * Create communication record in database
   */
  private async createCommunicationRecord(
    email: EmailMessage,
    analysis: EmailAnalysis,
    customerId: string
  ): Promise<string> {
    try {
      const communication = await prisma.communication.create({
        data: {
          customerId,
          userId: await this.getDefaultUserId(),
          channel: 'EMAIL',
          direction: 'INBOUND',
          subject: email.subject,
          content: email.body,
          timestamp: email.timestamp,
          read: false,
          metadata: {
            analysis,
            originalFrom: email.from,
            originalTo: email.to,
            headers: email.headers,
          },
          attachments: email.attachments ? {
            createMany: {
              data: email.attachments.map(att => ({
                name: att.name,
                type: att.contentType,
                url: `email-attachments/${email.id}/${att.name}`,
              })),
            },
          } : undefined,
        },
      });

      return communication.id;
    } catch (error) {
      console.error('Error creating communication record:', error);
      throw new Error('Failed to create communication record');
    }
  }

  /**
   * Perform automatic actions based on analysis
   */
  private async performAutomaticActions(
    email: EmailMessage,
    analysis: EmailAnalysis,
    customerId: string
  ): Promise<{ actions: string[]; serviceOrderId?: string }> {
    const actions: string[] = [];
    let serviceOrderId: string | undefined;

    try {
      // Auto-create service order for urgent service requests
      if (analysis.category === 'SERVICE_REQUEST' && analysis.priority === 'URGENT') {
        const serviceOrder = await this.createAutoServiceOrder(email, analysis, customerId);
        if (serviceOrder) {
          serviceOrderId = serviceOrder.id;
          actions.push('Created urgent service order');
        }
      }

      // Send auto-response if suggested
      if (analysis.autoResponse && analysis.confidence > 0.7) {
        await this.sendAutoResponse(email, analysis, customerId);
        actions.push('Sent automatic response');
      }

      // Create notification for high priority items
      if (analysis.priority === 'HIGH' || analysis.priority === 'URGENT') {
        await this.createPriorityNotification(email, analysis, customerId);
        actions.push('Created priority notification');
      }

      // Update customer status if it's a new inquiry
      if (analysis.category === 'INQUIRY' || analysis.category === 'QUOTE_REQUEST') {
        await this.updateCustomerStatus(customerId, 'PROSPECT');
        actions.push('Updated customer status to prospect');
      }

    } catch (error) {
      console.error('Error performing automatic actions:', error);
      actions.push('Some automatic actions failed');
    }

    return { actions, serviceOrderId };
  }

  /**
   * Determine if manual review is required
   */
  private requiresManualReview(analysis: EmailAnalysis): boolean {
    return (
      analysis.confidence < 0.6 ||
      analysis.category === 'COMPLAINT' ||
      analysis.priority === 'URGENT' ||
      analysis.sentiment === 'NEGATIVE'
    );
  }

  /**
   * Extract name from email address
   */
  private extractNameFromEmail(email: string): string {
    const localPart = email.split('@')[0];
    return localPart
      .replace(/[._-]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  }

  /**
   * Get default user ID (implement based on your auth system)
   */
  private async getDefaultUserId(): Promise<string> {
    // This should be implemented based on your authentication system
    // For now, return a placeholder
    const defaultUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
    });

    if (!defaultUser) {
      throw new Error('No default user found for email processing');
    }

    return defaultUser.id;
  }

  /**
   * Create automatic service order for urgent requests
   */
  private async createAutoServiceOrder(
    email: EmailMessage,
    analysis: EmailAnalysis,
    customerId: string
  ): Promise<any> {
    try {
      const serviceOrderData = {
        customerId,
        title: `Urgent Service Request - ${analysis.extractedData.equipmentType || 'HVAC'}`,
        description: analysis.extractedData.problemDescription || email.body.substring(0, 500),
        priority: analysis.priority,
        status: 'PENDING',
        serviceType: analysis.extractedData.serviceType || 'repair',
        source: 'EMAIL_AUTO',
        notes: `Auto-created from email: ${email.subject}`,
      };

      return await createServiceOrder(serviceOrderData);
    } catch (error) {
      console.error('Error creating auto service order:', error);
      return null;
    }
  }

  /**
   * Send automatic response to customer
   */
  private async sendAutoResponse(
    email: EmailMessage,
    analysis: EmailAnalysis,
    customerId: string
  ): Promise<void> {
    try {
      await sendCustomerCommunication({
        customerId,
        userId: await this.getDefaultUserId(),
        channel: 'EMAIL',
        subject: `Re: ${email.subject}`,
        content: analysis.autoResponse!,
        direction: 'OUTBOUND',
      });
    } catch (error) {
      console.error('Error sending auto response:', error);
    }
  }

  /**
   * Create priority notification for team
   */
  private async createPriorityNotification(
    email: EmailMessage,
    analysis: EmailAnalysis,
    customerId: string
  ): Promise<void> {
    try {
      await prisma.notification.create({
        data: {
          userId: await this.getDefaultUserId(),
          type: 'EMAIL_PRIORITY',
          title: `${analysis.priority} Email: ${email.subject}`,
          message: `New ${analysis.category.toLowerCase()} from ${email.from}`,
          data: {
            emailId: email.id,
            customerId,
            analysis,
          },
          read: false,
        },
      });
    } catch (error) {
      console.error('Error creating priority notification:', error);
    }
  }

  /**
   * Update customer status
   */
  private async updateCustomerStatus(customerId: string, status: string): Promise<void> {
    try {
      await prisma.customer.update({
        where: { id: customerId },
        data: { status },
      });
    } catch (error) {
      console.error('Error updating customer status:', error);
    }
  }
}

// Export singleton instance
export const emailIntelligenceService = new EmailIntelligenceService();

// Export convenience functions
export const processIncomingEmail = (email: EmailMessage) =>
  emailIntelligenceService.processIncomingEmail(email);
