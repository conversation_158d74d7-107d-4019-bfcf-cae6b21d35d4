// 📈 Forecasting Service - AI-Powered Sales Forecasting Engine
// Claude 4 w Augment Framework - DIVINE INTELLIGENCE! 🧠

import type {
  ForecastEntry,
  ForecastPeriod,
  AIModel,
  HistoricalSalesData,
  ForecastAccuracy,
  ForecastScenario,
  ForecastAnalytics,
  ForecastConfig
} from "~/models/forecasting";
import { gobackendClient } from "~/lib/gobackend-client";
import { OpportunityService } from "./opportunity.service";

export class ForecastingService {

  // 🚀 Core Forecasting Operations

  static async generateForecast(
    periodStart: Date,
    periodEnd: Date,
    options?: {
      model?: AIModel;
      includeScenarios?: boolean;
      includeBreakdowns?: boolean;
      confidenceLevel?: number;
    }
  ): Promise<ForecastEntry> {
    try {
      const config = await this.getForecastConfig();
      const model = options?.model || config.primaryModel;

      // Gather historical data
      const historicalData = await this.getHistoricalSalesData(
        this.getTrainingPeriodStart(periodStart, config.trainingPeriod),
        periodStart
      );

      // Validate data quality
      if (!this.validateDataQuality(historicalData, config)) {
        throw new Error('Insufficient data quality for reliable forecasting');
      }

      // Generate base forecast using AI
      const baseForecast = await this.generateAIForecast(
        historicalData,
        periodStart,
        periodEnd,
        model
      );

      // Apply seasonal adjustments
      const seasonallyAdjusted = await this.applySeasonalAdjustments(
        baseForecast,
        historicalData,
        periodStart,
        periodEnd
      );

      // Apply external factors
      const finalForecast = await this.applyExternalFactors(
        seasonallyAdjusted,
        periodStart,
        periodEnd
      );

      // Generate breakdowns if requested
      const breakdowns = options?.includeBreakdowns
        ? await this.generateForecastBreakdowns(finalForecast, historicalData)
        : undefined;

      // Create forecast entry
      const forecastEntry: ForecastEntry = {
        id: this.generateId(),
        name: `Forecast ${this.formatPeriod(periodStart, periodEnd)}`,
        periodStart,
        periodEnd,
        periodType: this.determinePeriodType(periodStart, periodEnd),
        predictedRevenue: finalForecast.predictedRevenue,
        lowerBound: finalForecast.lowerBound,
        upperBound: finalForecast.upperBound,
        confidence: finalForecast.confidence,
        modelUsed: model,
        modelVersion: await this.getModelVersion(model),
        trainingDataPeriod: `${config.trainingPeriod} months`,
        seasonalFactors: finalForecast.seasonalFactors,
        trendAnalysis: finalForecast.trendAnalysis,
        externalFactors: finalForecast.externalFactors,
        ...breakdowns,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'system', // TODO: Get from context
        status: 'active'
      };

      // Save forecast
      await this.saveForecast(forecastEntry);

      // Generate scenarios if requested
      if (options?.includeScenarios) {
        await this.generateForecastScenarios(forecastEntry);
      }

      return forecastEntry;
    } catch (error) {
      console.error('Error generating forecast:', error);
      throw new Error(`Failed to generate forecast: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // 🤖 AI-Powered Forecast Generation

  static async generateAIForecast(
    historicalData: HistoricalSalesData[],
    periodStart: Date,
    periodEnd: Date,
    model: AIModel
  ) {
    try {
      const prompt = this.buildForecastPrompt(historicalData, periodStart, periodEnd);

      let aiResponse;

      switch (model) {
        case 'bielik-v3':
          aiResponse = await gobackendClient.ai.forecast.mutate({
            prompt,
            model: 'bielik-v3',
            context: 'sales_forecasting',
            parameters: {
              temperature: 0.1, // Low temperature for consistent predictions
              max_tokens: 2000,
              include_confidence: true,
              include_bounds: true
            }
          });
          break;

        case 'gemma3-4b':
          aiResponse = await gobackendClient.ai.forecast.mutate({
            prompt,
            model: 'gemma3-4b',
            context: 'time_series_prediction',
            parameters: {
              temperature: 0.2,
              max_tokens: 1500,
              include_uncertainty: true
            }
          });
          break;

        case 'ensemble':
          // Use multiple models and combine results
          const [bielikResult, gemmaResult] = await Promise.all([
            this.generateAIForecast(historicalData, periodStart, periodEnd, 'bielik-v3'),
            this.generateAIForecast(historicalData, periodStart, periodEnd, 'gemma3-4b')
          ]);

          return this.combineEnsembleResults([bielikResult, gemmaResult]);

        default:
          throw new Error(`Unsupported AI model: ${model}`);
      }

      return this.parseAIForecastResponse(aiResponse);
    } catch (error) {
      console.error('Error in AI forecast generation:', error);
      // Fallback to statistical methods
      return this.generateStatisticalForecast(historicalData, periodStart, periodEnd);
    }
  }

  static buildForecastPrompt(
    historicalData: HistoricalSalesData[],
    periodStart: Date,
    periodEnd: Date
  ): string {
    const dataPoints = historicalData.length;
    const avgRevenue = historicalData.reduce((sum, d) => sum + d.revenue, 0) / dataPoints;
    const recentTrend = this.calculateRecentTrend(historicalData);
    const seasonalPattern = this.identifySeasonalPattern(historicalData);

    return `
    Analyze this HVAC business sales data and predict revenue for the specified period.

    HISTORICAL DATA SUMMARY:
    - Data points: ${dataPoints}
    - Average monthly revenue: ${avgRevenue.toFixed(0)} PLN
    - Recent trend: ${recentTrend > 0 ? 'Growing' : recentTrend < 0 ? 'Declining' : 'Stable'} (${(recentTrend * 100).toFixed(1)}%)
    - Seasonal pattern detected: ${seasonalPattern ? 'Yes' : 'No'}

    FORECAST PERIOD: ${periodStart.toISOString().split('T')[0]} to ${periodEnd.toISOString().split('T')[0]}

    HISTORICAL REVENUE DATA:
    ${historicalData.slice(-12).map(d =>
      `${d.date.toISOString().split('T')[0]}: ${d.revenue} PLN (${d.opportunityCount} opportunities)`
    ).join('\n')}

    BUSINESS CONTEXT:
    - Industry: HVAC (heating, ventilation, air conditioning)
    - Market: Warsaw, Poland and surrounding areas
    - Services: Installation, maintenance, repair
    - Brands: LG, Daikin (premium market)
    - Seasonality: High demand in summer (cooling) and winter (heating)

    REQUIREMENTS:
    1. Predict total revenue for the forecast period
    2. Provide confidence interval (lower and upper bounds)
    3. Estimate confidence level (0-100%)
    4. Identify key factors influencing the forecast
    5. Consider seasonal patterns and market trends

    Return JSON format:
    {
      "predictedRevenue": number,
      "lowerBound": number,
      "upperBound": number,
      "confidence": number,
      "keyFactors": string[],
      "seasonalAdjustment": number,
      "trendAdjustment": number,
      "reasoning": string
    }
    `;
  }

  // 📊 Historical Data Analysis

  static async getHistoricalSalesData(
    startDate: Date,
    endDate: Date
  ): Promise<HistoricalSalesData[]> {
    try {
      // Get opportunities data
      const opportunities = await OpportunityService.list({
        dateRange: { from: startDate, to: endDate },
        limit: 10000
      });

      // Group by month and calculate metrics
      const monthlyData = new Map<string, HistoricalSalesData>();

      opportunities.forEach(opp => {
        if (opp.stage === 'closed_won' && opp.actualCloseDate) {
          const monthKey = opp.actualCloseDate.toISOString().substring(0, 7); // YYYY-MM

          if (!monthlyData.has(monthKey)) {
            monthlyData.set(monthKey, {
              id: this.generateId(),
              date: new Date(monthKey + '-01'),
              revenue: 0,
              opportunityCount: 0,
              serviceBreakdown: {},
              sourceBreakdown: {},
              ownerBreakdown: {},
              regionBreakdown: {},
              seasonalFactors: {},
              externalFactors: {},
              marketConditions: {},
              dataQuality: 100,
              completeness: 100,
              source: 'crm_data'
            });
          }

          const data = monthlyData.get(monthKey)!;
          data.revenue += opp.value;
          data.opportunityCount += 1;

          // Update breakdowns
          data.serviceBreakdown[opp.serviceType] = (data.serviceBreakdown[opp.serviceType] || 0) + opp.value;
          data.sourceBreakdown[opp.source] = (data.sourceBreakdown[opp.source] || 0) + opp.value;
          data.ownerBreakdown[opp.ownerId] = (data.ownerBreakdown[opp.ownerId] || 0) + opp.value;
        }
      });

      return Array.from(monthlyData.values()).sort((a, b) => a.date.getTime() - b.date.getTime());
    } catch (error) {
      console.error('Error fetching historical sales data:', error);
      throw new Error('Failed to fetch historical sales data');
    }
  }

  // 🔄 Seasonal Adjustments

  static async applySeasonalAdjustments(
    baseForecast: any,
    historicalData: HistoricalSalesData[],
    periodStart: Date,
    periodEnd: Date
  ) {
    const seasonalFactors = this.calculateSeasonalFactors(historicalData);
    const forecastMonth = periodStart.getMonth();
    const seasonalAdjustment = seasonalFactors[forecastMonth] || 1.0;

    return {
      ...baseForecast,
      predictedRevenue: baseForecast.predictedRevenue * seasonalAdjustment,
      lowerBound: baseForecast.lowerBound * seasonalAdjustment,
      upperBound: baseForecast.upperBound * seasonalAdjustment,
      seasonalFactors: seasonalFactors
    };
  }

  // 🌍 External Factors Integration

  static async applyExternalFactors(
    forecast: any,
    periodStart: Date,
    periodEnd: Date
  ) {
    // Get external factors (weather, economic indicators, etc.)
    const externalFactors = await this.getExternalFactors(periodStart, periodEnd);

    let adjustment = 1.0;
    externalFactors.forEach(factor => {
      adjustment *= (1 + factor.impact / 100);
    });

    return {
      ...forecast,
      predictedRevenue: forecast.predictedRevenue * adjustment,
      lowerBound: forecast.lowerBound * adjustment,
      upperBound: forecast.upperBound * adjustment,
      externalFactors
    };
  }

  // 📈 Forecast Analytics

  static async generateForecastAnalytics(
    forecastId: string,
    actualData?: HistoricalSalesData[]
  ): Promise<ForecastAnalytics> {
    try {
      const forecast = await this.getForecast(forecastId);

      if (!forecast) {
        throw new Error('Forecast not found');
      }

      // Calculate accuracy if actual data is available
      let accuracy = 0;
      let variance = 0;

      if (actualData && actualData.length > 0) {
        const actualRevenue = actualData.reduce((sum, d) => sum + d.revenue, 0);
        variance = ((actualRevenue - forecast.predictedRevenue) / forecast.predictedRevenue) * 100;
        accuracy = Math.max(0, 100 - Math.abs(variance));
      }

      // Generate insights using AI
      const insights = await this.generateAIInsights(forecast, actualData);

      return {
        id: this.generateId(),
        periodStart: forecast.periodStart,
        periodEnd: forecast.periodEnd,
        forecastAccuracy: accuracy,
        revenueVariance: variance,
        volumeVariance: 0, // TODO: Calculate from opportunity count
        revenueGrowth: 0, // TODO: Calculate from historical comparison
        volumeGrowth: 0,
        seasonalityStrength: 0,
        nextPeriodPrediction: forecast.predictedRevenue * 1.05, // Simple growth assumption
        confidenceLevel: forecast.confidence,
        keyDrivers: insights.keyDrivers,
        actionableInsights: insights.actionableInsights,
        optimizationOpportunities: insights.optimizationOpportunities,
        riskMitigations: insights.riskMitigations,
        generatedAt: new Date(),
        generatedBy: 'ai_analytics'
      };
    } catch (error) {
      console.error('Error generating forecast analytics:', error);
      throw new Error('Failed to generate forecast analytics');
    }
  }

  // 🛠️ Helper Methods

  private static calculateRecentTrend(data: HistoricalSalesData[]): number {
    if (data.length < 2) return 0;

    const recent = data.slice(-3);
    const older = data.slice(-6, -3);

    const recentAvg = recent.reduce((sum, d) => sum + d.revenue, 0) / recent.length;
    const olderAvg = older.reduce((sum, d) => sum + d.revenue, 0) / older.length;

    return olderAvg > 0 ? (recentAvg - olderAvg) / olderAvg : 0;
  }

  private static identifySeasonalPattern(data: HistoricalSalesData[]): boolean {
    // Simple seasonal pattern detection
    if (data.length < 12) return false;

    const monthlyAvgs = new Array(12).fill(0);
    const monthlyCounts = new Array(12).fill(0);

    data.forEach(d => {
      const month = d.date.getMonth();
      monthlyAvgs[month] += d.revenue;
      monthlyCounts[month]++;
    });

    // Calculate coefficient of variation
    const avgRevenues = monthlyAvgs.map((sum, i) => monthlyCounts[i] > 0 ? sum / monthlyCounts[i] : 0);
    const mean = avgRevenues.reduce((sum, val) => sum + val, 0) / 12;
    const variance = avgRevenues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / 12;
    const cv = Math.sqrt(variance) / mean;

    return cv > 0.2; // Threshold for seasonal pattern
  }

  private static calculateSeasonalFactors(data: HistoricalSalesData[]): Record<number, number> {
    const factors: Record<number, number> = {};

    // Calculate average revenue per month
    const monthlyTotals = new Array(12).fill(0);
    const monthlyCounts = new Array(12).fill(0);

    data.forEach(d => {
      const month = d.date.getMonth();
      monthlyTotals[month] += d.revenue;
      monthlyCounts[month]++;
    });

    const overallAvg = data.reduce((sum, d) => sum + d.revenue, 0) / data.length;

    for (let month = 0; month < 12; month++) {
      if (monthlyCounts[month] > 0) {
        const monthlyAvg = monthlyTotals[month] / monthlyCounts[month];
        factors[month] = monthlyAvg / overallAvg;
      } else {
        factors[month] = 1.0;
      }
    }

    return factors;
  }

  private static async getExternalFactors(startDate: Date, endDate: Date) {
    // Mock external factors - in real implementation, integrate with weather APIs, economic data, etc.
    return [
      {
        name: 'Summer season',
        type: 'seasonal' as const,
        impact: 15, // 15% increase in demand
        probability: 90,
        description: 'High demand for air conditioning installation and maintenance'
      },
      {
        name: 'Economic growth',
        type: 'economic' as const,
        impact: 5,
        probability: 70,
        description: 'Positive economic indicators supporting construction and renovation'
      }
    ];
  }

  private static generateId(): string {
    return `forecast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static formatPeriod(start: Date, end: Date): string {
    return `${start.toISOString().split('T')[0]} to ${end.toISOString().split('T')[0]}`;
  }

  private static determinePeriodType(start: Date, end: Date): ForecastPeriod {
    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

    if (days <= 7) return 'weekly';
    if (days <= 31) return 'monthly';
    if (days <= 93) return 'quarterly';
    if (days <= 366) return 'yearly';
    return 'custom';
  }

  // 🔧 Additional Service Methods

  private static async getForecastConfig(): Promise<ForecastConfig> {
    // Default configuration - in real implementation, fetch from database
    return {
      id: 'default',
      name: 'Default Forecast Configuration',
      primaryModel: 'bielik-v3',
      fallbackModel: 'gemma3-4b',
      trainingPeriod: 12,
      minimumDataPoints: 6,
      dataQualityThreshold: 80,
      defaultPeriod: 'monthly',
      confidenceLevel: 95,
      updateFrequency: 'weekly',
      enableSeasonality: true,
      enableTrends: true,
      enableExternalFactors: true,
      enableCompetitorAnalysis: false,
      crossValidationFolds: 5,
      holdoutPeriod: 3,
      accuracyThreshold: 85,
      alertOnLowAccuracy: true,
      alertOnSignificantChange: true,
      alertThreshold: 20,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };
  }

  private static getTrainingPeriodStart(forecastStart: Date, trainingMonths: number): Date {
    const start = new Date(forecastStart);
    start.setMonth(start.getMonth() - trainingMonths);
    return start;
  }

  private static validateDataQuality(data: HistoricalSalesData[], config: ForecastConfig): boolean {
    if (data.length < config.minimumDataPoints) return false;

    const avgQuality = data.reduce((sum, d) => sum + d.dataQuality, 0) / data.length;
    return avgQuality >= config.dataQualityThreshold;
  }

  private static async getModelVersion(model: AIModel): Promise<string> {
    switch (model) {
      case 'bielik-v3': return 'v3.1.0';
      case 'gemma3-4b': return 'v4.2.1';
      default: return 'v1.0.0';
    }
  }

  private static parseAIForecastResponse(response: any) {
    try {
      const parsed = typeof response.result === 'string'
        ? JSON.parse(response.result)
        : response.result;

      return {
        predictedRevenue: parsed.predictedRevenue || 0,
        lowerBound: parsed.lowerBound || parsed.predictedRevenue * 0.8,
        upperBound: parsed.upperBound || parsed.predictedRevenue * 1.2,
        confidence: parsed.confidence || 75,
        keyFactors: parsed.keyFactors || [],
        seasonalAdjustment: parsed.seasonalAdjustment || 1.0,
        trendAdjustment: parsed.trendAdjustment || 1.0,
        reasoning: parsed.reasoning || 'AI-generated forecast'
      };
    } catch (error) {
      console.error('Error parsing AI forecast response:', error);
      throw new Error('Invalid AI forecast response format');
    }
  }

  private static combineEnsembleResults(results: any[]): any {
    const weights = [0.6, 0.4]; // Bielik V3 gets higher weight

    const weightedRevenue = results.reduce((sum, result, index) =>
      sum + (result.predictedRevenue * weights[index]), 0
    );

    const weightedLower = results.reduce((sum, result, index) =>
      sum + (result.lowerBound * weights[index]), 0
    );

    const weightedUpper = results.reduce((sum, result, index) =>
      sum + (result.upperBound * weights[index]), 0
    );

    const avgConfidence = results.reduce((sum, result) =>
      sum + result.confidence, 0
    ) / results.length;

    return {
      predictedRevenue: weightedRevenue,
      lowerBound: weightedLower,
      upperBound: weightedUpper,
      confidence: avgConfidence,
      keyFactors: [...new Set(results.flatMap(r => r.keyFactors))],
      reasoning: 'Ensemble model combining multiple AI predictions'
    };
  }

  private static generateStatisticalForecast(
    data: HistoricalSalesData[],
    periodStart: Date,
    periodEnd: Date
  ) {
    // Simple moving average fallback
    const recentData = data.slice(-6); // Last 6 months
    const avgRevenue = recentData.reduce((sum, d) => sum + d.revenue, 0) / recentData.length;

    // Calculate standard deviation for confidence bounds
    const variance = recentData.reduce((sum, d) =>
      sum + Math.pow(d.revenue - avgRevenue, 2), 0
    ) / recentData.length;
    const stdDev = Math.sqrt(variance);

    return {
      predictedRevenue: avgRevenue,
      lowerBound: avgRevenue - (1.96 * stdDev), // 95% confidence interval
      upperBound: avgRevenue + (1.96 * stdDev),
      confidence: 60, // Lower confidence for statistical method
      keyFactors: ['Historical average', 'Statistical trend'],
      reasoning: 'Statistical forecast based on historical moving average'
    };
  }

  private static async saveForecast(forecast: ForecastEntry): Promise<void> {
    try {
      await gobackendClient.forecast.create.mutate(forecast);
    } catch (error) {
      console.error('Error saving forecast:', error);
      throw new Error('Failed to save forecast');
    }
  }

  private static async getForecast(id: string): Promise<ForecastEntry | null> {
    try {
      return await gobackendClient.forecast.get.query({ id });
    } catch (error) {
      console.error('Error fetching forecast:', error);
      return null;
    }
  }

  private static async generateForecastBreakdowns(forecast: any, historicalData: HistoricalSalesData[]) {
    // Generate service breakdown
    const serviceBreakdown = await this.generateServiceForecast(forecast, historicalData);
    const sourceBreakdown = await this.generateSourceForecast(forecast, historicalData);
    const ownerBreakdown = await this.generateOwnerForecast(forecast, historicalData);

    return {
      forecastByService: serviceBreakdown,
      forecastBySource: sourceBreakdown,
      forecastByOwner: ownerBreakdown
    };
  }

  private static async generateServiceForecast(forecast: any, historicalData: HistoricalSalesData[]) {
    // Analyze historical service distribution
    const serviceDistribution = this.calculateServiceDistribution(historicalData);

    return Object.entries(serviceDistribution).map(([service, percentage]) => ({
      serviceType: service,
      predictedRevenue: forecast.predictedRevenue * percentage,
      opportunityCount: Math.round(10 * percentage), // Estimate
      averageDealSize: (forecast.predictedRevenue * percentage) / Math.max(1, Math.round(10 * percentage)),
      confidence: forecast.confidence * 0.9 // Slightly lower confidence for breakdowns
    }));
  }

  private static async generateSourceForecast(forecast: any, historicalData: HistoricalSalesData[]) {
    const sourceDistribution = this.calculateSourceDistribution(historicalData);

    return Object.entries(sourceDistribution).map(([source, percentage]) => ({
      source,
      predictedRevenue: forecast.predictedRevenue * percentage,
      leadCount: Math.round(50 * percentage), // Estimate
      conversionRate: 0.2, // 20% average conversion
      costPerLead: 100, // Estimate
      roi: 5.0 // 5x ROI estimate
    }));
  }

  private static async generateOwnerForecast(forecast: any, historicalData: HistoricalSalesData[]) {
    const ownerDistribution = this.calculateOwnerDistribution(historicalData);

    return Object.entries(ownerDistribution).map(([ownerId, percentage]) => ({
      ownerId,
      ownerName: `Owner ${ownerId}`, // TODO: Get real names
      predictedRevenue: forecast.predictedRevenue * percentage,
      opportunityCount: Math.round(15 * percentage),
      winRate: 0.7, // 70% average win rate
      averageDealSize: (forecast.predictedRevenue * percentage) / Math.max(1, Math.round(15 * percentage))
    }));
  }

  private static calculateServiceDistribution(data: HistoricalSalesData[]): Record<string, number> {
    const totalRevenue = data.reduce((sum, d) => sum + d.revenue, 0);
    const serviceRevenue: Record<string, number> = {};

    data.forEach(d => {
      Object.entries(d.serviceBreakdown).forEach(([service, revenue]) => {
        serviceRevenue[service] = (serviceRevenue[service] || 0) + revenue;
      });
    });

    const distribution: Record<string, number> = {};
    Object.entries(serviceRevenue).forEach(([service, revenue]) => {
      distribution[service] = totalRevenue > 0 ? revenue / totalRevenue : 0;
    });

    return distribution;
  }

  private static calculateSourceDistribution(data: HistoricalSalesData[]): Record<string, number> {
    const totalRevenue = data.reduce((sum, d) => sum + d.revenue, 0);
    const sourceRevenue: Record<string, number> = {};

    data.forEach(d => {
      Object.entries(d.sourceBreakdown).forEach(([source, revenue]) => {
        sourceRevenue[source] = (sourceRevenue[source] || 0) + revenue;
      });
    });

    const distribution: Record<string, number> = {};
    Object.entries(sourceRevenue).forEach(([source, revenue]) => {
      distribution[source] = totalRevenue > 0 ? revenue / totalRevenue : 0;
    });

    return distribution;
  }

  private static calculateOwnerDistribution(data: HistoricalSalesData[]): Record<string, number> {
    const totalRevenue = data.reduce((sum, d) => sum + d.revenue, 0);
    const ownerRevenue: Record<string, number> = {};

    data.forEach(d => {
      Object.entries(d.ownerBreakdown).forEach(([owner, revenue]) => {
        ownerRevenue[owner] = (ownerRevenue[owner] || 0) + revenue;
      });
    });

    const distribution: Record<string, number> = {};
    Object.entries(ownerRevenue).forEach(([owner, revenue]) => {
      distribution[owner] = totalRevenue > 0 ? revenue / totalRevenue : 0;
    });

    return distribution;
  }

  private static async generateForecastScenarios(forecast: ForecastEntry): Promise<void> {
    // Generate optimistic, pessimistic, and realistic scenarios
    const scenarios = [
      {
        name: 'Optimistic',
        adjustment: 1.2,
        description: 'Best case scenario with favorable market conditions'
      },
      {
        name: 'Pessimistic',
        adjustment: 0.8,
        description: 'Conservative scenario with challenging market conditions'
      },
      {
        name: 'Realistic',
        adjustment: 1.0,
        description: 'Most likely scenario based on current trends'
      }
    ];

    for (const scenario of scenarios) {
      await this.createForecastScenario(forecast, scenario);
    }
  }

  private static async createForecastScenario(forecast: ForecastEntry, scenario: any): Promise<void> {
    const scenarioData: ForecastScenario = {
      id: this.generateId(),
      name: scenario.name,
      description: scenario.description,
      baselineAdjustment: (scenario.adjustment - 1) * 100,
      seasonalityAdjustment: {},
      externalFactorAdjustments: {},
      predictedRevenue: forecast.predictedRevenue * scenario.adjustment,
      confidenceInterval: [
        forecast.lowerBound * scenario.adjustment,
        forecast.upperBound * scenario.adjustment
      ],
      probability: scenario.name === 'Realistic' ? 60 : 20,
      impactFactors: [],
      riskAssessment: {
        overallRisk: scenario.name === 'Pessimistic' ? 'high' : 'medium',
        riskFactors: [],
        mitigationStrategies: [],
        contingencyPlans: []
      },
      createdAt: new Date(),
      createdBy: 'system'
    };

    try {
      await gobackendClient.forecastScenario.create.mutate(scenarioData);
    } catch (error) {
      console.error('Error creating forecast scenario:', error);
    }
  }

  private static async generateAIInsights(forecast: ForecastEntry, actualData?: HistoricalSalesData[]) {
    // Mock AI insights - in real implementation, use AI to generate insights
    return {
      keyDrivers: ['Seasonal demand', 'Market growth', 'Service expansion'],
      actionableInsights: [
        'Focus on installation services during peak season',
        'Increase marketing spend for maintenance services',
        'Expand service area to capture more market share'
      ],
      optimizationOpportunities: [
        'Optimize pricing for premium services',
        'Improve conversion rates from website leads',
        'Develop partnership channels'
      ],
      riskMitigations: [
        'Diversify service offerings',
        'Build stronger customer retention programs',
        'Monitor competitor pricing strategies'
      ]
    };
  }

  // 📋 Additional Service Methods for Route Integration

  static async list(filters?: {
    periodType?: ForecastPeriod;
    model?: AIModel;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<ForecastEntry[]> {
    try {
      return await gobackendClient.forecast.list.query(filters);
    } catch (error) {
      console.error('Error listing forecasts:', error);
      return [];
    }
  }

  static async update(id: string, updates: Partial<ForecastEntry>): Promise<ForecastEntry> {
    try {
      return await gobackendClient.forecast.update.mutate({ id, ...updates });
    } catch (error) {
      console.error('Error updating forecast:', error);
      throw new Error('Failed to update forecast');
    }
  }

  static async delete(id: string): Promise<void> {
    try {
      await gobackendClient.forecast.delete.mutate({ id });
    } catch (error) {
      console.error('Error deleting forecast:', error);
      throw new Error('Failed to delete forecast');
    }
  }

  static async exportForecasts(forecastIds: string[], format: string): Promise<any> {
    try {
      const forecasts = await Promise.all(
        forecastIds.map(id => this.getForecast(id))
      );

      if (format === 'csv') {
        return this.convertToCSV(forecasts.filter(Boolean));
      } else if (format === 'json') {
        return forecasts.filter(Boolean);
      }

      throw new Error(`Unsupported export format: ${format}`);
    } catch (error) {
      console.error('Error exporting forecasts:', error);
      throw new Error('Failed to export forecasts');
    }
  }

  static async createScenario(forecastId: string, scenarioData: any): Promise<any> {
    try {
      const forecast = await this.getForecast(forecastId);
      if (!forecast) {
        throw new Error('Forecast not found');
      }

      return await this.createForecastScenario(forecast, scenarioData);
    } catch (error) {
      console.error('Error creating scenario:', error);
      throw new Error('Failed to create scenario');
    }
  }

  static async validateModel(model: AIModel, testPeriod: number): Promise<any> {
    try {
      // Get historical data for validation
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - testPeriod);

      const historicalData = await this.getHistoricalSalesData(startDate, endDate);

      // Split data into training and test sets
      const splitIndex = Math.floor(historicalData.length * 0.8);
      const trainingData = historicalData.slice(0, splitIndex);
      const testData = historicalData.slice(splitIndex);

      // Generate forecasts for test period
      const forecasts = [];
      for (const testPoint of testData) {
        const forecast = await this.generateAIForecast(
          trainingData,
          testPoint.date,
          new Date(testPoint.date.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days later
          model
        );
        forecasts.push({
          predicted: forecast.predictedRevenue,
          actual: testPoint.revenue,
          date: testPoint.date
        });
      }

      // Calculate validation metrics
      const mape = this.calculateMAPE(forecasts);
      const rmse = this.calculateRMSE(forecasts);
      const r2 = this.calculateR2(forecasts);

      return {
        model,
        testPeriod,
        accuracy: Math.max(0, 100 - mape),
        mape,
        rmse,
        r2,
        forecasts
      };
    } catch (error) {
      console.error('Error validating model:', error);
      throw new Error('Failed to validate model');
    }
  }

  private static convertToCSV(forecasts: ForecastEntry[]): string {
    const headers = [
      'ID', 'Name', 'Period Start', 'Period End', 'Predicted Revenue',
      'Lower Bound', 'Upper Bound', 'Confidence', 'Model Used', 'Created At'
    ];

    const rows = forecasts.map(f => [
      f.id,
      f.name,
      f.periodStart.toISOString(),
      f.periodEnd.toISOString(),
      f.predictedRevenue,
      f.lowerBound,
      f.upperBound,
      f.confidence,
      f.modelUsed,
      f.createdAt.toISOString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  private static calculateMAPE(forecasts: any[]): number {
    if (forecasts.length === 0) return 100;

    const totalError = forecasts.reduce((sum, f) => {
      const error = Math.abs((f.actual - f.predicted) / f.actual) * 100;
      return sum + (isFinite(error) ? error : 0);
    }, 0);

    return totalError / forecasts.length;
  }

  private static calculateRMSE(forecasts: any[]): number {
    if (forecasts.length === 0) return 0;

    const sumSquaredErrors = forecasts.reduce((sum, f) => {
      const error = f.actual - f.predicted;
      return sum + (error * error);
    }, 0);

    return Math.sqrt(sumSquaredErrors / forecasts.length);
  }

  private static calculateR2(forecasts: any[]): number {
    if (forecasts.length === 0) return 0;

    const actualMean = forecasts.reduce((sum, f) => sum + f.actual, 0) / forecasts.length;

    const totalSumSquares = forecasts.reduce((sum, f) => {
      const diff = f.actual - actualMean;
      return sum + (diff * diff);
    }, 0);

    const residualSumSquares = forecasts.reduce((sum, f) => {
      const diff = f.actual - f.predicted;
      return sum + (diff * diff);
    }, 0);

    return totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;
  }
}
