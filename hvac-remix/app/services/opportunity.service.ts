// 🎯 Opportunity Service - AI-Powered Sales Pipeline Management
// Claude 4 w Augment Framework - DIVINE QUALITY! 💎

import type { 
  Opportunity, 
  OpportunityStage, 
  LeadScoringFactors,
  SalesForecast,
  OpportunityAnalytics,
  PipelineConfig
} from "~/models/opportunity";
import { gobackendClient } from "~/lib/gobackend-client";

export class OpportunityService {
  
  // 🚀 CRUD Operations with AI Enhancement
  
  static async list(filters?: {
    stage?: OpportunityStage;
    ownerId?: string;
    customerId?: string;
    source?: string;
    minValue?: number;
    maxValue?: number;
    dateRange?: { from: Date; to: Date };
    search?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      const opportunities = await gobackendClient.opportunity.list.query(filters);
      
      // Enhance with AI insights
      const enhancedOpportunities = await Promise.all(
        opportunities.map(async (opp) => ({
          ...opp,
          aiInsights: await this.generateAIInsights(opp),
          recommendedActions: await this.getRecommendedActions(opp)
        }))
      );
      
      return enhancedOpportunities;
    } catch (error) {
      console.error('Error listing opportunities:', error);
      throw new Error('Failed to fetch opportunities');
    }
  }

  static async get(id: string) {
    try {
      const opportunity = await gobackendClient.opportunity.get.query({ id });
      
      // Enhance with comprehensive AI analysis
      const [aiInsights, leadScore, winProbability, competitorAnalysis] = await Promise.all([
        this.generateAIInsights(opportunity),
        this.calculateLeadScore(opportunity),
        this.predictWinProbability(opportunity),
        this.analyzeCompetition(opportunity)
      ]);
      
      return {
        ...opportunity,
        aiInsights,
        leadScore,
        winProbabilityAI: winProbability,
        competitorAnalysis,
        recommendedActions: await this.getRecommendedActions(opportunity)
      };
    } catch (error) {
      console.error('Error fetching opportunity:', error);
      throw new Error('Failed to fetch opportunity');
    }
  }

  static async create(data: Partial<Opportunity>) {
    try {
      // Calculate initial AI scores
      const leadScore = await this.calculateLeadScore(data as Opportunity);
      const winProbability = await this.predictWinProbability(data as Opportunity);
      
      const opportunityData = {
        ...data,
        leadScore,
        winProbabilityAI: winProbability,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active',
        activitiesCount: 0,
        emailsCount: 0,
        callsCount: 0,
        quoteGenerated: false,
        contractSigned: false,
        siteVisitRequired: this.requiresSiteVisit(data),
        siteVisitCompleted: false
      };
      
      const opportunity = await gobackendClient.opportunity.create.mutate(opportunityData);
      
      // Trigger automation workflows
      await this.triggerAutomationWorkflows(opportunity, 'created');
      
      return opportunity;
    } catch (error) {
      console.error('Error creating opportunity:', error);
      throw new Error('Failed to create opportunity');
    }
  }

  static async update(id: string, data: Partial<Opportunity>) {
    try {
      const currentOpportunity = await this.get(id);
      
      // Recalculate AI scores if relevant fields changed
      let updates = { ...data, updatedAt: new Date() };
      
      if (this.shouldRecalculateScores(data)) {
        const leadScore = await this.calculateLeadScore({ ...currentOpportunity, ...data });
        const winProbability = await this.predictWinProbability({ ...currentOpportunity, ...data });
        
        updates = {
          ...updates,
          leadScore,
          winProbabilityAI: winProbability
        };
      }
      
      const opportunity = await gobackendClient.opportunity.update.mutate({ id, ...updates });
      
      // Trigger automation workflows if stage changed
      if (data.stage && data.stage !== currentOpportunity.stage) {
        await this.triggerAutomationWorkflows(opportunity, 'stage_changed');
      }
      
      return opportunity;
    } catch (error) {
      console.error('Error updating opportunity:', error);
      throw new Error('Failed to update opportunity');
    }
  }

  static async delete(id: string) {
    try {
      return await gobackendClient.opportunity.delete.mutate({ id });
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      throw new Error('Failed to delete opportunity');
    }
  }

  // 🧠 AI-Powered Lead Scoring
  
  static async calculateLeadScore(opportunity: Opportunity): Promise<number> {
    try {
      const factors = await this.analyzeLeadScoringFactors(opportunity);
      
      // Weighted scoring algorithm
      const weights = {
        demographic: 0.15,
        behavioral: 0.25,
        firmographic: 0.30,
        technographic: 0.15,
        engagement: 0.10,
        intent: 0.05
      };
      
      const score = Object.entries(factors).reduce((total, [key, value]) => {
        return total + (value * weights[key as keyof typeof weights]);
      }, 0);
      
      return Math.round(Math.min(100, Math.max(0, score)));
    } catch (error) {
      console.error('Error calculating lead score:', error);
      return 50; // Default score
    }
  }

  static async analyzeLeadScoringFactors(opportunity: Opportunity): Promise<LeadScoringFactors> {
    // AI-powered analysis using Bielik V3/Gemma3
    const prompt = `
    Analyze this HVAC opportunity for lead scoring:
    
    Customer: ${opportunity.customer?.name}
    Service Type: ${opportunity.serviceType}
    Building Type: ${opportunity.buildingType}
    Value: ${opportunity.value} PLN
    Source: ${opportunity.source}
    
    Rate each factor from 0-100:
    1. Demographic fit (location, company size)
    2. Behavioral signals (engagement, responsiveness)
    3. Firmographic match (budget, authority, need, timeline)
    4. Technographic fit (current HVAC setup)
    5. Engagement level (meetings, calls, emails)
    6. Purchase intent (urgency, decision timeline)
    
    Return JSON with scores for each factor.
    `;
    
    try {
      const aiResponse = await gobackendClient.ai.analyze.mutate({
        prompt,
        model: 'bielik-v3',
        context: 'lead_scoring'
      });
      
      return JSON.parse(aiResponse.result);
    } catch (error) {
      console.error('Error in AI lead scoring:', error);
      // Fallback to rule-based scoring
      return this.fallbackLeadScoring(opportunity);
    }
  }

  // 📊 Win Probability Prediction
  
  static async predictWinProbability(opportunity: Opportunity): Promise<number> {
    try {
      const historicalData = await this.getHistoricalOpportunities({
        serviceType: opportunity.serviceType,
        buildingType: opportunity.buildingType,
        valueRange: this.getValueRange(opportunity.value)
      });
      
      const prompt = `
      Predict win probability for this HVAC opportunity based on historical data:
      
      Current Opportunity:
      - Stage: ${opportunity.stage}
      - Value: ${opportunity.value} PLN
      - Service: ${opportunity.serviceType}
      - Building: ${opportunity.buildingType}
      - Lead Score: ${opportunity.leadScore}
      - Days in Pipeline: ${this.getDaysInPipeline(opportunity)}
      
      Historical Similar Opportunities: ${historicalData.length} cases
      Historical Win Rate: ${this.calculateHistoricalWinRate(historicalData)}%
      
      Consider factors:
      - Current stage probability
      - Lead score correlation
      - Seasonal trends
      - Competition level
      - Customer engagement
      
      Return probability as number 0-100.
      `;
      
      const aiResponse = await gobackendClient.ai.predict.mutate({
        prompt,
        model: 'gemma3-4b',
        context: 'win_probability'
      });
      
      const probability = parseInt(aiResponse.result);
      return Math.min(100, Math.max(0, probability));
    } catch (error) {
      console.error('Error predicting win probability:', error);
      return this.getDefaultProbabilityByStage(opportunity.stage);
    }
  }

  // 🎯 Recommended Actions
  
  static async getRecommendedActions(opportunity: Opportunity): Promise<string[]> {
    const actions: string[] = [];
    
    // Stage-specific recommendations
    switch (opportunity.stage) {
      case 'lead':
        actions.push('Schedule qualification call');
        actions.push('Send welcome email with company info');
        break;
      case 'qualified':
        actions.push('Conduct needs analysis');
        actions.push('Schedule site visit if required');
        break;
      case 'needs_analysis':
        actions.push('Prepare detailed proposal');
        actions.push('Calculate accurate pricing');
        break;
      case 'proposal':
        actions.push('Follow up on proposal');
        actions.push('Address any concerns');
        break;
      case 'negotiation':
        actions.push('Finalize terms and conditions');
        actions.push('Prepare contract');
        break;
    }
    
    // AI-powered recommendations
    if (opportunity.leadScore < 50) {
      actions.push('Improve lead qualification');
    }
    
    if (opportunity.lastContactDate && this.daysSinceLastContact(opportunity) > 7) {
      actions.push('Schedule follow-up contact');
    }
    
    if (opportunity.siteVisitRequired && !opportunity.siteVisitCompleted) {
      actions.push('Schedule site visit');
    }
    
    return actions;
  }

  // 📈 Sales Forecasting
  
  static async generateSalesForecast(period: string): Promise<SalesForecast> {
    try {
      const opportunities = await this.getActiveOpportunities();
      const historicalData = await this.getHistoricalData(period);
      
      const totalValue = opportunities.reduce((sum, opp) => sum + opp.value, 0);
      const weightedValue = opportunities.reduce((sum, opp) => 
        sum + (opp.value * (opp.probability / 100)), 0
      );
      
      const winRate = this.calculateWinRate(historicalData);
      const averageDealSize = this.calculateAverageDealSize(historicalData);
      const averageSalesCycle = this.calculateAverageSalesCycle(historicalData);
      
      return {
        period,
        totalValue,
        weightedValue,
        opportunitiesCount: opportunities.length,
        averageDealSize,
        winRate,
        averageSalesCycle,
        confidence: await this.calculateForecastConfidence(opportunities),
        trends: await this.analyzeTrends(historicalData)
      };
    } catch (error) {
      console.error('Error generating sales forecast:', error);
      throw new Error('Failed to generate sales forecast');
    }
  }

  // 🔄 Pipeline Management
  
  static async moveToNextStage(opportunityId: string): Promise<Opportunity> {
    const opportunity = await this.get(opportunityId);
    const nextStage = this.getNextStage(opportunity.stage);
    
    if (!nextStage) {
      throw new Error('Opportunity is already in final stage');
    }
    
    return this.update(opportunityId, {
      stage: nextStage,
      probability: this.getDefaultProbabilityByStage(nextStage)
    });
  }

  static async moveToPreviousStage(opportunityId: string): Promise<Opportunity> {
    const opportunity = await this.get(opportunityId);
    const previousStage = this.getPreviousStage(opportunity.stage);
    
    if (!previousStage) {
      throw new Error('Opportunity is already in first stage');
    }
    
    return this.update(opportunityId, {
      stage: previousStage,
      probability: this.getDefaultProbabilityByStage(previousStage)
    });
  }

  // 🤖 Automation & Workflows
  
  static async triggerAutomationWorkflows(opportunity: Opportunity, trigger: string) {
    try {
      await gobackendClient.workflow.trigger.mutate({
        entityType: 'opportunity',
        entityId: opportunity.id,
        trigger,
        data: opportunity
      });
    } catch (error) {
      console.error('Error triggering automation workflows:', error);
    }
  }

  // 🛠️ Helper Methods
  
  private static shouldRecalculateScores(data: Partial<Opportunity>): boolean {
    const scoringFields = ['value', 'serviceType', 'buildingType', 'source', 'stage'];
    return scoringFields.some(field => field in data);
  }

  private static requiresSiteVisit(data: Partial<Opportunity>): boolean {
    return data.serviceType === 'installation' || 
           data.installationComplexity === 'complex' ||
           data.installationComplexity === 'extreme';
  }

  private static getDefaultProbabilityByStage(stage: OpportunityStage): number {
    const probabilities = {
      'lead': 10,
      'qualified': 25,
      'needs_analysis': 40,
      'proposal': 60,
      'negotiation': 80,
      'closed_won': 100,
      'closed_lost': 0,
      'on_hold': 30
    };
    return probabilities[stage] || 50;
  }

  private static getNextStage(currentStage: OpportunityStage): OpportunityStage | null {
    const stageOrder = ['lead', 'qualified', 'needs_analysis', 'proposal', 'negotiation', 'closed_won'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] as OpportunityStage : null;
  }

  private static getPreviousStage(currentStage: OpportunityStage): OpportunityStage | null {
    const stageOrder = ['lead', 'qualified', 'needs_analysis', 'proposal', 'negotiation', 'closed_won'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex > 0 ? stageOrder[currentIndex - 1] as OpportunityStage : null;
  }

  private static daysSinceLastContact(opportunity: Opportunity): number {
    if (!opportunity.lastContactDate) return 999;
    return Math.floor((Date.now() - opportunity.lastContactDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private static getDaysInPipeline(opportunity: Opportunity): number {
    return Math.floor((Date.now() - opportunity.createdAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  private static fallbackLeadScoring(opportunity: Opportunity): LeadScoringFactors {
    // Rule-based fallback scoring
    return {
      demographic: opportunity.buildingType === 'office' ? 80 : 60,
      behavioral: 70,
      firmographic: opportunity.value > 10000 ? 90 : 50,
      technographic: 65,
      engagement: 70,
      intent: opportunity.serviceType === 'emergency' ? 95 : 60
    };
  }
}
