/**
 * Enhanced Communication Hub - Unified Communication Management
 * Integrates all communication channels with AI-powered insights
 * Part of FAZA 1: Komunik<PERSON>ja & Przetwar<PERSON>ie <PERSON>ych
 */

import { prisma } from '~/db.server';
import { bielikService } from './bielik.server';
import { emailIntelligenceService, type EmailMessage } from './email-intelligence.server';
import { callTranscriptionService, type CallRecord } from './call-transcription.server';
import { sendCustomerCommunication } from './communication.service';

export interface CommunicationChannel {
  id: string;
  type: 'EMAIL' | 'PHONE' | 'SMS' | 'CHAT' | 'WHATSAPP' | 'SOCIAL';
  name: string;
  status: 'ACTIVE' | 'INACTIVE' | 'ERROR';
  config: Record<string, any>;
  lastSync?: Date;
  messageCount: number;
}

export interface UnifiedMessage {
  id: string;
  customerId: string;
  channel: string;
  direction: 'INBOUND' | 'OUTBOUND';
  subject?: string;
  content: string;
  timestamp: Date;
  read: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE';
  category: string;
  metadata: Record<string, any>;
  attachments?: any[];
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
}

export interface CommunicationInsights {
  totalMessages: number;
  unreadCount: number;
  channelBreakdown: Record<string, number>;
  sentimentAnalysis: {
    positive: number;
    neutral: number;
    negative: number;
  };
  priorityBreakdown: Record<string, number>;
  responseTimeAvg: number;
  topCategories: Array<{ category: string; count: number }>;
  trends: {
    daily: Array<{ date: string; count: number }>;
    weekly: Array<{ week: string; count: number }>;
  };
}

export interface AutoResponseTemplate {
  id: string;
  name: string;
  trigger: {
    channel?: string;
    category?: string;
    keywords?: string[];
    sentiment?: string;
  };
  template: string;
  variables: string[];
  active: boolean;
}

/**
 * Enhanced Communication Hub Service
 */
class EnhancedCommunicationHubService {
  private channels: Map<string, CommunicationChannel> = new Map();
  private autoResponseTemplates: Map<string, AutoResponseTemplate> = new Map();

  constructor() {
    this.initializeChannels();
    this.loadAutoResponseTemplates();
  }

  /**
   * Initialize communication channels
   */
  private async initializeChannels(): Promise<void> {
    const defaultChannels: CommunicationChannel[] = [
      {
        id: 'email',
        type: 'EMAIL',
        name: 'Email',
        status: 'ACTIVE',
        config: {
          imapHost: process.env.EMAIL_IMAP_HOST,
          smtpHost: process.env.EMAIL_SMTP_HOST,
        },
        messageCount: 0,
      },
      {
        id: 'phone',
        type: 'PHONE',
        name: 'Phone',
        status: 'ACTIVE',
        config: {
          provider: process.env.VOIP_PROVIDER,
          webhookUrl: process.env.VOIP_WEBHOOK_URL,
        },
        messageCount: 0,
      },
      {
        id: 'sms',
        type: 'SMS',
        name: 'SMS',
        status: 'ACTIVE',
        config: {
          provider: process.env.SMS_PROVIDER,
          apiKey: process.env.SMS_API_KEY,
        },
        messageCount: 0,
      },
    ];

    defaultChannels.forEach(channel => {
      this.channels.set(channel.id, channel);
    });
  }

  /**
   * Load auto-response templates
   */
  private async loadAutoResponseTemplates(): Promise<void> {
    try {
      const templates = await prisma.communicationTemplate.findMany({
        where: { type: 'AUTO_RESPONSE' },
      });

      templates.forEach(template => {
        this.autoResponseTemplates.set(template.id, {
          id: template.id,
          name: template.name,
          trigger: template.metadata?.trigger || {},
          template: template.content,
          variables: template.metadata?.variables || [],
          active: template.metadata?.active || false,
        });
      });
    } catch (error) {
      console.error('Error loading auto-response templates:', error);
    }
  }

  /**
   * Process incoming message from any channel
   */
  async processIncomingMessage(
    channelId: string,
    message: EmailMessage | CallRecord | any
  ): Promise<any> {
    try {
      const channel = this.channels.get(channelId);
      if (!channel) {
        throw new Error(`Unknown channel: ${channelId}`);
      }

      let result: any;

      switch (channel.type) {
        case 'EMAIL':
          result = await emailIntelligenceService.processIncomingEmail(message as EmailMessage);
          break;
        case 'PHONE':
          result = await callTranscriptionService.processCallRecording(message as CallRecord);
          break;
        default:
          result = await this.processGenericMessage(channelId, message);
      }

      // Update channel message count
      channel.messageCount++;
      channel.lastSync = new Date();

      // Check for auto-response triggers
      await this.checkAutoResponseTriggers(result);

      return result;
    } catch (error) {
      console.error(`Error processing message from ${channelId}:`, error);
      throw error;
    }
  }

  /**
   * Get unified message feed for customer
   */
  async getUnifiedMessageFeed(
    customerId: string,
    options: {
      limit?: number;
      offset?: number;
      channels?: string[];
      unreadOnly?: boolean;
    } = {}
  ): Promise<UnifiedMessage[]> {
    try {
      const whereClause: any = { customerId };

      if (options.channels?.length) {
        whereClause.channel = { in: options.channels };
      }

      if (options.unreadOnly) {
        whereClause.read = false;
      }

      const communications = await prisma.communication.findMany({
        where: whereClause,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
          attachments: true,
        },
        orderBy: { timestamp: 'desc' },
        take: options.limit || 50,
        skip: options.offset || 0,
      });

      return communications.map(comm => ({
        id: comm.id,
        customerId: comm.customerId,
        channel: comm.channel,
        direction: comm.direction as 'INBOUND' | 'OUTBOUND',
        subject: comm.subject || undefined,
        content: comm.content,
        timestamp: comm.timestamp,
        read: comm.read,
        priority: (comm.metadata as any)?.analysis?.priority || 'MEDIUM',
        sentiment: (comm.metadata as any)?.analysis?.sentiment || 'NEUTRAL',
        category: (comm.metadata as any)?.analysis?.category || 'OTHER',
        metadata: comm.metadata as Record<string, any>,
        attachments: comm.attachments,
        customer: comm.customer,
      }));
    } catch (error) {
      console.error('Error getting unified message feed:', error);
      throw new Error('Failed to get unified message feed');
    }
  }

  /**
   * Get communication insights and analytics
   */
  async getCommunicationInsights(
    timeRange: { start: Date; end: Date },
    customerId?: string
  ): Promise<CommunicationInsights> {
    try {
      const whereClause: any = {
        timestamp: {
          gte: timeRange.start,
          lte: timeRange.end,
        },
      };

      if (customerId) {
        whereClause.customerId = customerId;
      }

      const communications = await prisma.communication.findMany({
        where: whereClause,
        include: {
          customer: true,
        },
      });

      // Calculate insights
      const totalMessages = communications.length;
      const unreadCount = communications.filter(c => !c.read).length;

      const channelBreakdown: Record<string, number> = {};
      const sentimentCounts = { positive: 0, neutral: 0, negative: 0 };
      const priorityBreakdown: Record<string, number> = {};
      const categoryCount: Record<string, number> = {};

      let totalResponseTime = 0;
      let responseTimeCount = 0;

      communications.forEach(comm => {
        // Channel breakdown
        channelBreakdown[comm.channel] = (channelBreakdown[comm.channel] || 0) + 1;

        // Sentiment analysis
        const sentiment = (comm.metadata as any)?.analysis?.sentiment || 'neutral';
        if (sentiment.toLowerCase() in sentimentCounts) {
          sentimentCounts[sentiment.toLowerCase() as keyof typeof sentimentCounts]++;
        }

        // Priority breakdown
        const priority = (comm.metadata as any)?.analysis?.priority || 'MEDIUM';
        priorityBreakdown[priority] = (priorityBreakdown[priority] || 0) + 1;

        // Category breakdown
        const category = (comm.metadata as any)?.analysis?.category || 'OTHER';
        categoryCount[category] = (categoryCount[category] || 0) + 1;

        // Response time calculation (simplified)
        if (comm.direction === 'OUTBOUND' && comm.metadata) {
          const responseTime = (comm.metadata as any).responseTime;
          if (responseTime) {
            totalResponseTime += responseTime;
            responseTimeCount++;
          }
        }
      });

      const responseTimeAvg = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

      const topCategories = Object.entries(categoryCount)
        .map(([category, count]) => ({ category, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Generate trends (simplified)
      const trends = {
        daily: this.generateDailyTrends(communications, timeRange),
        weekly: this.generateWeeklyTrends(communications, timeRange),
      };

      return {
        totalMessages,
        unreadCount,
        channelBreakdown,
        sentimentAnalysis: sentimentCounts,
        priorityBreakdown,
        responseTimeAvg,
        topCategories,
        trends,
      };
    } catch (error) {
      console.error('Error getting communication insights:', error);
      throw new Error('Failed to get communication insights');
    }
  }

  /**
   * Send unified message across channels
   */
  async sendUnifiedMessage(
    customerId: string,
    channels: string[],
    content: {
      subject?: string;
      message: string;
      template?: string;
      variables?: Record<string, string>;
    }
  ): Promise<{ success: string[]; failed: string[] }> {
    const success: string[] = [];
    const failed: string[] = [];

    for (const channelId of channels) {
      try {
        const channel = this.channels.get(channelId);
        if (!channel) {
          failed.push(`Unknown channel: ${channelId}`);
          continue;
        }

        let finalContent = content.message;
        if (content.template && content.variables) {
          finalContent = this.processTemplate(content.template, content.variables);
        }

        await sendCustomerCommunication({
          customerId,
          userId: await this.getDefaultUserId(),
          channel: channel.type,
          subject: content.subject,
          content: finalContent,
          direction: 'OUTBOUND',
        });

        success.push(channelId);
      } catch (error) {
        console.error(`Error sending message via ${channelId}:`, error);
        failed.push(`${channelId}: ${error.message}`);
      }
    }

    return { success, failed };
  }

  /**
   * Process generic message (for non-email/phone channels)
   */
  private async processGenericMessage(channelId: string, message: any): Promise<any> {
    // Basic processing for other channels
    const analysis = await this.analyzeGenericMessage(message);

    // Create communication record
    const communication = await prisma.communication.create({
      data: {
        customerId: message.customerId || await this.findCustomerByContact(message.from),
        userId: await this.getDefaultUserId(),
        channel: channelId.toUpperCase(),
        direction: 'INBOUND',
        subject: message.subject,
        content: message.content || message.text,
        timestamp: message.timestamp || new Date(),
        read: false,
        metadata: { analysis, originalMessage: message },
      },
    });

    return {
      messageId: message.id,
      analysis,
      communicationId: communication.id,
      autoActionsPerformed: [],
      requiresManualReview: analysis.confidence < 0.6,
    };
  }

  /**
   * Analyze generic message with AI
   */
  private async analyzeGenericMessage(message: any): Promise<any> {
    try {
      const prompt = `
Analyze this customer message and provide insights:

Content: ${message.content || message.text}
Channel: ${message.channel || 'unknown'}

Provide analysis in JSON format with category, priority, sentiment, and confidence.
      `;

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
      });

      // Parse response (simplified)
      return {
        category: 'OTHER',
        priority: 'MEDIUM',
        sentiment: 'NEUTRAL',
        confidence: 0.5,
        summary: response.content.substring(0, 200),
      };
    } catch (error) {
      console.error('Error analyzing generic message:', error);
      return {
        category: 'OTHER',
        priority: 'MEDIUM',
        sentiment: 'NEUTRAL',
        confidence: 0.3,
        summary: 'Analysis failed',
      };
    }
  }

  /**
   * Check for auto-response triggers
   */
  private async checkAutoResponseTriggers(result: any): Promise<void> {
    try {
      for (const [templateId, template] of this.autoResponseTemplates) {
        if (!template.active) continue;

        const shouldTrigger = this.evaluateAutoResponseTrigger(template, result);

        if (shouldTrigger) {
          await this.sendAutoResponse(template, result);
          break; // Send only one auto-response per message
        }
      }
    } catch (error) {
      console.error('Error checking auto-response triggers:', error);
    }
  }

  /**
   * Evaluate if auto-response should trigger
   */
  private evaluateAutoResponseTrigger(template: AutoResponseTemplate, result: any): boolean {
    const { trigger } = template;
    const analysis = result.analysis;

    // Check channel match
    if (trigger.channel && trigger.channel !== result.channel) {
      return false;
    }

    // Check category match
    if (trigger.category && trigger.category !== analysis.category) {
      return false;
    }

    // Check sentiment match
    if (trigger.sentiment && trigger.sentiment !== analysis.sentiment) {
      return false;
    }

    // Check keywords
    if (trigger.keywords?.length) {
      const content = (result.content || '').toLowerCase();
      const hasKeyword = trigger.keywords.some(keyword =>
        content.includes(keyword.toLowerCase())
      );
      if (!hasKeyword) {
        return false;
      }
    }

    return true;
  }

  /**
   * Send auto-response
   */
  private async sendAutoResponse(template: AutoResponseTemplate, result: any): Promise<void> {
    try {
      const variables = this.extractVariablesFromResult(result);
      const content = this.processTemplate(template.template, variables);

      await sendCustomerCommunication({
        customerId: result.customerId,
        userId: await this.getDefaultUserId(),
        channel: result.channel || 'EMAIL',
        subject: `Re: ${result.subject || 'Your inquiry'}`,
        content,
        direction: 'OUTBOUND',
        metadata: {
          autoResponse: true,
          templateId: template.id,
        },
      });

      console.log(`Auto-response sent using template: ${template.name}`);
    } catch (error) {
      console.error('Error sending auto-response:', error);
    }
  }

  /**
   * Extract variables from processing result
   */
  private extractVariablesFromResult(result: any): Record<string, string> {
    const analysis = result.analysis || {};
    const extractedData = analysis.extractedData || {};

    return {
      customerName: extractedData.customerName || 'Valued Customer',
      equipmentType: extractedData.equipmentType || 'HVAC system',
      problemDescription: extractedData.problemDescription || 'your inquiry',
      urgency: extractedData.urgency || 'standard',
      currentDate: new Date().toLocaleDateString('pl-PL'),
      currentTime: new Date().toLocaleTimeString('pl-PL'),
    };
  }

  /**
   * Process template with variables
   */
  private processTemplate(template: string, variables: Record<string, string>): string {
    let processed = template;

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processed = processed.replace(regex, value);
    });

    return processed;
  }

  /**
   * Find customer by contact information
   */
  private async findCustomerByContact(contact: string): Promise<string> {
    try {
      let customer = await prisma.customer.findFirst({
        where: {
          OR: [
            { email: contact },
            { phone: contact },
          ],
        },
      });

      if (!customer) {
        // Create new lead
        customer = await prisma.customer.create({
          data: {
            name: `Customer ${contact}`,
            email: contact.includes('@') ? contact : null,
            phone: !contact.includes('@') ? contact : null,
            status: 'LEAD',
            source: 'COMMUNICATION',
            userId: await this.getDefaultUserId(),
          },
        });
      }

      return customer.id;
    } catch (error) {
      console.error('Error finding/creating customer:', error);
      throw new Error('Failed to process customer');
    }
  }

  /**
   * Generate daily trends
   */
  private generateDailyTrends(
    communications: any[],
    timeRange: { start: Date; end: Date }
  ): Array<{ date: string; count: number }> {
    const dailyCounts: Record<string, number> = {};

    communications.forEach(comm => {
      const date = comm.timestamp.toISOString().split('T')[0];
      dailyCounts[date] = (dailyCounts[date] || 0) + 1;
    });

    const trends: Array<{ date: string; count: number }> = [];
    const currentDate = new Date(timeRange.start);

    while (currentDate <= timeRange.end) {
      const dateStr = currentDate.toISOString().split('T')[0];
      trends.push({
        date: dateStr,
        count: dailyCounts[dateStr] || 0,
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return trends;
  }

  /**
   * Generate weekly trends
   */
  private generateWeeklyTrends(
    communications: any[],
    timeRange: { start: Date; end: Date }
  ): Array<{ week: string; count: number }> {
    const weeklyCounts: Record<string, number> = {};

    communications.forEach(comm => {
      const date = new Date(comm.timestamp);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekStr = weekStart.toISOString().split('T')[0];
      weeklyCounts[weekStr] = (weeklyCounts[weekStr] || 0) + 1;
    });

    return Object.entries(weeklyCounts).map(([week, count]) => ({
      week,
      count,
    }));
  }

  /**
   * Get default user ID
   */
  private async getDefaultUserId(): Promise<string> {
    const defaultUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
    });

    if (!defaultUser) {
      throw new Error('No default user found for communication processing');
    }

    return defaultUser.id;
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(messageIds: string[]): Promise<void> {
    try {
      await prisma.communication.updateMany({
        where: {
          id: { in: messageIds },
        },
        data: {
          read: true,
        },
      });
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw new Error('Failed to mark messages as read');
    }
  }

  /**
   * Get channel status
   */
  getChannelStatus(): CommunicationChannel[] {
    return Array.from(this.channels.values());
  }

  /**
   * Update channel configuration
   */
  async updateChannelConfig(channelId: string, config: Record<string, any>): Promise<void> {
    const channel = this.channels.get(channelId);
    if (!channel) {
      throw new Error(`Channel not found: ${channelId}`);
    }

    channel.config = { ...channel.config, ...config };
    channel.lastSync = new Date();
  }

  /**
   * Create new auto-response template
   */
  async createAutoResponseTemplate(template: Omit<AutoResponseTemplate, 'id'>): Promise<string> {
    try {
      const created = await prisma.communicationTemplate.create({
        data: {
          name: template.name,
          type: 'AUTO_RESPONSE',
          content: template.template,
          metadata: {
            trigger: template.trigger,
            variables: template.variables,
            active: template.active,
          },
        },
      });

      this.autoResponseTemplates.set(created.id, {
        ...template,
        id: created.id,
      });

      return created.id;
    } catch (error) {
      console.error('Error creating auto-response template:', error);
      throw new Error('Failed to create auto-response template');
    }
  }
}

// Export singleton instance
export const enhancedCommunicationHub = new EnhancedCommunicationHubService();

// Export convenience functions
export const processIncomingMessage = (channelId: string, message: any) =>
  enhancedCommunicationHub.processIncomingMessage(channelId, message);

export const getUnifiedMessageFeed = (customerId: string, options?: any) =>
  enhancedCommunicationHub.getUnifiedMessageFeed(customerId, options);

export const getCommunicationInsights = (timeRange: any, customerId?: string) =>
  enhancedCommunicationHub.getCommunicationInsights(timeRange, customerId);

export const sendUnifiedMessage = (customerId: string, channels: string[], content: any) =>
  enhancedCommunicationHub.sendUnifiedMessage(customerId, channels, content);
