/**
 * Call Transcription Service - VoIP Integration & AI Analysis
 * Implements comprehensive call analysis using Bielik V3 AI
 * Part of FAZA 1: Komunikacja & Przetwarzanie Danych
 */

import { prisma } from '~/db.server';
import { bielikService } from './bielik.server';
import { sendCustomerCommunication } from './communication.service';
import { createServiceOrder } from './service-order.service';

export interface CallRecord {
  id: string;
  from: string;
  to: string;
  duration: number;
  timestamp: Date;
  direction: 'INBOUND' | 'OUTBOUND';
  status: 'COMPLETED' | 'MISSED' | 'BUSY' | 'FAILED';
  recordingUrl?: string;
  metadata?: Record<string, any>;
}

export interface TranscriptionResult {
  text: string;
  confidence: number;
  segments: TranscriptionSegment[];
  language: string;
  duration: number;
}

export interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
  speaker?: 'AGENT' | 'CUSTOMER';
  confidence: number;
}

export interface CallAnalysis {
  intent: 'SERVICE_REQUEST' | 'COMPLAINT' | 'INQUIRY' | 'QUOTE_REQUEST' | 'FOLLOW_UP' | 'SALES' | 'OTHER';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE';
  emotion: 'CALM' | 'FRUSTRATED' | 'ANGRY' | 'SATISFIED' | 'CONFUSED' | 'URGENT';
  confidence: number;
  keyTopics: string[];
  extractedData: {
    customerName?: string;
    phoneNumber?: string;
    address?: string;
    equipmentType?: string;
    problemDescription?: string;
    appointmentRequest?: string;
    urgencyLevel?: string;
  };
  actionItems: string[];
  followUpRequired: boolean;
  summary: string;
}

export interface CallProcessingResult {
  callId: string;
  transcription: TranscriptionResult;
  analysis: CallAnalysis;
  customerId?: string;
  serviceOrderId?: string;
  communicationId: string;
  autoActionsPerformed: string[];
  requiresFollowUp: boolean;
}

/**
 * Main Call Transcription Service
 */
class CallTranscriptionService {
  private voipConfig: {
    provider: 'twilio' | 'freepbx' | 'asterisk';
    apiKey?: string;
    apiSecret?: string;
    webhookUrl: string;
  };

  private transcriptionConfig: {
    provider: 'openai' | 'google' | 'azure' | 'local';
    apiKey?: string;
    model: string;
    language: string;
  };

  constructor() {
    this.voipConfig = {
      provider: (process.env.VOIP_PROVIDER as any) || 'twilio',
      apiKey: process.env.VOIP_API_KEY,
      apiSecret: process.env.VOIP_API_SECRET,
      webhookUrl: process.env.VOIP_WEBHOOK_URL || '/api/voip/webhook',
    };

    this.transcriptionConfig = {
      provider: (process.env.TRANSCRIPTION_PROVIDER as any) || 'openai',
      apiKey: process.env.TRANSCRIPTION_API_KEY,
      model: process.env.TRANSCRIPTION_MODEL || 'whisper-1',
      language: 'pl', // Polish language support
    };
  }

  /**
   * Process call recording with transcription and AI analysis
   */
  async processCallRecording(call: CallRecord): Promise<CallProcessingResult> {
    try {
      console.log(`Processing call: ${call.id} from ${call.from}`);

      // 1. Transcribe the call recording
      const transcription = await this.transcribeCall(call);

      // 2. Analyze transcription with AI
      const analysis = await this.analyzeCallWithAI(transcription, call);

      // 3. Find or create customer
      const customerId = await this.findOrCreateCustomer(call, analysis);

      // 4. Create communication record
      const communicationId = await this.createCommunicationRecord(call, transcription, analysis, customerId);

      // 5. Perform automatic actions
      const autoActions = await this.performAutomaticActions(call, analysis, customerId);

      // 6. Determine if follow-up is required
      const requiresFollowUp = this.requiresFollowUp(analysis);

      return {
        callId: call.id,
        transcription,
        analysis,
        customerId,
        serviceOrderId: autoActions.serviceOrderId,
        communicationId,
        autoActionsPerformed: autoActions.actions,
        requiresFollowUp,
      };
    } catch (error) {
      console.error('Call processing error:', error);
      throw new Error(`Failed to process call: ${error.message}`);
    }
  }

  /**
   * Transcribe call recording using configured ASR service
   */
  private async transcribeCall(call: CallRecord): Promise<TranscriptionResult> {
    try {
      if (!call.recordingUrl) {
        throw new Error('No recording URL provided');
      }

      switch (this.transcriptionConfig.provider) {
        case 'openai':
          return await this.transcribeWithOpenAI(call.recordingUrl);
        case 'google':
          return await this.transcribeWithGoogle(call.recordingUrl);
        case 'azure':
          return await this.transcribeWithAzure(call.recordingUrl);
        default:
          return await this.transcribeWithMockService(call);
      }
    } catch (error) {
      console.error('Transcription error:', error);
      // Return mock transcription as fallback
      return await this.transcribeWithMockService(call);
    }
  }

  /**
   * Transcribe using OpenAI Whisper
   */
  private async transcribeWithOpenAI(recordingUrl: string): Promise<TranscriptionResult> {
    try {
      const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.transcriptionConfig.apiKey}`,
        },
        body: this.createFormData(recordingUrl),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const data = await response.json();

      return {
        text: data.text,
        confidence: 0.9, // OpenAI doesn't provide confidence scores
        segments: this.parseSegments(data.text),
        language: data.language || 'pl',
        duration: data.duration || 0,
      };
    } catch (error) {
      console.error('OpenAI transcription error:', error);
      throw error;
    }
  }

  /**
   * Analyze call transcription using Bielik V3 AI
   */
  private async analyzeCallWithAI(transcription: TranscriptionResult, call: CallRecord): Promise<CallAnalysis> {
    try {
      const prompt = this.buildCallAnalysisPrompt(transcription, call);

      const response = await bielikService.generateResponse({
        prompt,
        model: 'bielik-v3',
        temperature: 0.3,
        maxTokens: 2000,
      });

      return this.parseCallAnalysisResponse(response.content);
    } catch (error) {
      console.error('Call AI analysis error:', error);
      // Fallback to basic analysis
      return this.performBasicCallAnalysis(transcription, call);
    }
  }

  /**
   * Build comprehensive prompt for call analysis
   */
  private buildCallAnalysisPrompt(transcription: TranscriptionResult, call: CallRecord): string {
    return `
Analyze this HVAC business phone call and provide structured analysis:

CALL DETAILS:
- Direction: ${call.direction}
- Duration: ${call.duration} seconds
- From: ${call.from}
- To: ${call.to}
- Status: ${call.status}

TRANSCRIPTION:
${transcription.text}

Please analyze and respond with JSON format:
{
  "intent": "SERVICE_REQUEST|COMPLAINT|INQUIRY|QUOTE_REQUEST|FOLLOW_UP|SALES|OTHER",
  "priority": "LOW|MEDIUM|HIGH|URGENT",
  "sentiment": "POSITIVE|NEUTRAL|NEGATIVE",
  "emotion": "CALM|FRUSTRATED|ANGRY|SATISFIED|CONFUSED|URGENT",
  "confidence": 0.0-1.0,
  "keyTopics": ["topic1", "topic2", "topic3"],
  "extractedData": {
    "customerName": "extracted name or null",
    "phoneNumber": "extracted phone or null",
    "address": "extracted address or null",
    "equipmentType": "air_conditioning|heating|ventilation|heat_pump|other",
    "problemDescription": "brief description of the issue",
    "appointmentRequest": "requested appointment time or null",
    "urgencyLevel": "immediate|within_week|within_month|flexible"
  },
  "actionItems": ["action1", "action2", "action3"],
  "followUpRequired": true|false,
  "summary": "brief summary of the call"
}

Focus on HVAC-specific terminology, customer emotions, and urgency indicators.
    `.trim();
  }

  /**
   * Parse AI response into structured call analysis
   */
  private parseCallAnalysisResponse(content: string): CallAnalysis {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        intent: parsed.intent || 'OTHER',
        priority: parsed.priority || 'MEDIUM',
        sentiment: parsed.sentiment || 'NEUTRAL',
        emotion: parsed.emotion || 'CALM',
        confidence: parsed.confidence || 0.5,
        keyTopics: parsed.keyTopics || [],
        extractedData: parsed.extractedData || {},
        actionItems: parsed.actionItems || [],
        followUpRequired: parsed.followUpRequired || false,
        summary: parsed.summary || 'Call analysis completed',
      };
    } catch (error) {
      console.error('Failed to parse call analysis response:', error);
      return {
        intent: 'OTHER',
        priority: 'MEDIUM',
        sentiment: 'NEUTRAL',
        emotion: 'CALM',
        confidence: 0.3,
        keyTopics: [],
        extractedData: {},
        actionItems: ['Manual review required'],
        followUpRequired: true,
        summary: 'Call analysis failed - manual review needed',
      };
    }
  }

  /**
   * Create form data for file upload
   */
  private createFormData(recordingUrl: string): FormData {
    const formData = new FormData();
    // This would need to be implemented to fetch and upload the audio file
    // For now, this is a placeholder
    formData.append('file', recordingUrl);
    formData.append('model', this.transcriptionConfig.model);
    formData.append('language', this.transcriptionConfig.language);
    return formData;
  }

  /**
   * Parse transcription into segments
   */
  private parseSegments(text: string): TranscriptionSegment[] {
    // Basic implementation - would need more sophisticated parsing
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.map((sentence, index) => ({
      start: index * 5, // Rough estimate
      end: (index + 1) * 5,
      text: sentence.trim(),
      confidence: 0.9,
    }));
  }

  /**
   * Mock transcription service for development
   */
  private async transcribeWithMockService(call: CallRecord): Promise<TranscriptionResult> {
    return {
      text: `Mock transcription for call ${call.id}. Customer called about HVAC service request.`,
      confidence: 0.8,
      segments: [
        {
          start: 0,
          end: 5,
          text: 'Hello, I need help with my air conditioning.',
          speaker: 'CUSTOMER',
          confidence: 0.9,
        },
        {
          start: 5,
          end: 10,
          text: 'I can help you with that. What seems to be the problem?',
          speaker: 'AGENT',
          confidence: 0.9,
        },
      ],
      language: 'pl',
      duration: call.duration,
    };
  }

  /**
   * Perform basic call analysis without AI (fallback)
   */
  private performBasicCallAnalysis(transcription: TranscriptionResult, call: CallRecord): CallAnalysis {
    const text = transcription.text.toLowerCase();

    let intent: CallAnalysis['intent'] = 'OTHER';
    let priority: CallAnalysis['priority'] = 'MEDIUM';
    let sentiment: CallAnalysis['sentiment'] = 'NEUTRAL';

    // Basic keyword detection
    if (text.includes('urgent') || text.includes('emergency')) {
      priority = 'URGENT';
      intent = 'SERVICE_REQUEST';
    } else if (text.includes('complaint') || text.includes('problem')) {
      intent = 'COMPLAINT';
      priority = 'HIGH';
      sentiment = 'NEGATIVE';
    } else if (text.includes('quote') || text.includes('estimate')) {
      intent = 'QUOTE_REQUEST';
    } else if (text.includes('service') || text.includes('repair')) {
      intent = 'SERVICE_REQUEST';
    }

    return {
      intent,
      priority,
      sentiment,
      emotion: 'CALM',
      confidence: 0.4,
      keyTopics: ['hvac', 'service'],
      extractedData: {},
      actionItems: ['Manual review and follow-up needed'],
      followUpRequired: true,
      summary: 'Basic analysis completed - manual review recommended',
    };
  }

  /**
   * Find existing customer or create new lead from call
   */
  private async findOrCreateCustomer(call: CallRecord, analysis: CallAnalysis): Promise<string> {
    try {
      // Try to find existing customer by phone number
      let customer = await prisma.customer.findFirst({
        where: {
          phone: call.from,
        },
      });

      if (customer) {
        return customer.id;
      }

      // Try to find by extracted name and phone
      if (analysis.extractedData.customerName) {
        customer = await prisma.customer.findFirst({
          where: {
            name: { contains: analysis.extractedData.customerName, mode: 'insensitive' },
          },
        });

        if (customer) {
          // Update phone if not set
          if (!customer.phone) {
            await prisma.customer.update({
              where: { id: customer.id },
              data: { phone: call.from },
            });
          }
          return customer.id;
        }
      }

      // Create new customer/lead
      const newCustomer = await prisma.customer.create({
        data: {
          name: analysis.extractedData.customerName || `Customer ${call.from}`,
          phone: call.from,
          address: analysis.extractedData.address || null,
          status: 'LEAD',
          source: 'PHONE',
          userId: await this.getDefaultUserId(),
          notes: `Created from call: ${call.id}`,
        },
      });

      return newCustomer.id;
    } catch (error) {
      console.error('Error finding/creating customer from call:', error);
      throw new Error('Failed to process customer data from call');
    }
  }

  /**
   * Create communication record for the call
   */
  private async createCommunicationRecord(
    call: CallRecord,
    transcription: TranscriptionResult,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<string> {
    try {
      const communication = await prisma.communication.create({
        data: {
          customerId,
          userId: await this.getDefaultUserId(),
          channel: 'PHONE',
          direction: call.direction,
          subject: `Phone Call - ${analysis.intent}`,
          content: transcription.text,
          timestamp: call.timestamp,
          read: false,
          metadata: {
            callId: call.id,
            duration: call.duration,
            transcription,
            analysis,
            recordingUrl: call.recordingUrl,
          },
        },
      });

      return communication.id;
    } catch (error) {
      console.error('Error creating communication record for call:', error);
      throw new Error('Failed to create communication record for call');
    }
  }

  /**
   * Perform automatic actions based on call analysis
   */
  private async performAutomaticActions(
    call: CallRecord,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<{ actions: string[]; serviceOrderId?: string }> {
    const actions: string[] = [];
    let serviceOrderId: string | undefined;

    try {
      // Auto-create service order for urgent service requests
      if (analysis.intent === 'SERVICE_REQUEST' && analysis.priority === 'URGENT') {
        const serviceOrder = await this.createAutoServiceOrder(call, analysis, customerId);
        if (serviceOrder) {
          serviceOrderId = serviceOrder.id;
          actions.push('Created urgent service order from call');
        }
      }

      // Create follow-up task if required
      if (analysis.followUpRequired) {
        await this.createFollowUpTask(call, analysis, customerId);
        actions.push('Created follow-up task');
      }

      // Create notification for high priority calls
      if (analysis.priority === 'HIGH' || analysis.priority === 'URGENT') {
        await this.createPriorityNotification(call, analysis, customerId);
        actions.push('Created priority notification for call');
      }

      // Update customer status based on call intent
      if (analysis.intent === 'QUOTE_REQUEST') {
        await this.updateCustomerStatus(customerId, 'PROSPECT');
        actions.push('Updated customer status to prospect');
      }

      // Send follow-up email if appointment was requested
      if (analysis.extractedData.appointmentRequest) {
        await this.sendAppointmentFollowUp(call, analysis, customerId);
        actions.push('Sent appointment follow-up email');
      }

    } catch (error) {
      console.error('Error performing automatic actions for call:', error);
      actions.push('Some automatic actions failed');
    }

    return { actions, serviceOrderId };
  }

  /**
   * Determine if follow-up is required
   */
  private requiresFollowUp(analysis: CallAnalysis): boolean {
    return (
      analysis.followUpRequired ||
      analysis.intent === 'COMPLAINT' ||
      analysis.priority === 'URGENT' ||
      analysis.sentiment === 'NEGATIVE' ||
      analysis.extractedData.appointmentRequest !== undefined
    );
  }

  /**
   * Create automatic service order from call
   */
  private async createAutoServiceOrder(
    call: CallRecord,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<any> {
    try {
      const serviceOrderData = {
        customerId,
        title: `Urgent Service Request - ${analysis.extractedData.equipmentType || 'HVAC'}`,
        description: analysis.extractedData.problemDescription || analysis.summary,
        priority: analysis.priority,
        status: 'PENDING',
        serviceType: 'repair',
        source: 'PHONE_AUTO',
        notes: `Auto-created from call: ${call.id}`,
      };

      return await createServiceOrder(serviceOrderData);
    } catch (error) {
      console.error('Error creating auto service order from call:', error);
      return null;
    }
  }

  /**
   * Create follow-up task
   */
  private async createFollowUpTask(
    call: CallRecord,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<void> {
    try {
      await prisma.task.create({
        data: {
          title: `Follow-up: ${analysis.intent} Call`,
          description: `Follow up on call from ${call.from}: ${analysis.summary}`,
          priority: analysis.priority,
          status: 'PENDING',
          dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
          assignedUserId: await this.getDefaultUserId(),
          customerId,
          metadata: {
            callId: call.id,
            analysis,
          },
        },
      });
    } catch (error) {
      console.error('Error creating follow-up task:', error);
    }
  }

  /**
   * Send appointment follow-up email
   */
  private async sendAppointmentFollowUp(
    call: CallRecord,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<void> {
    try {
      const emailContent = `
Dear Customer,

Thank you for calling us today. We understand you would like to schedule an appointment for ${analysis.extractedData.equipmentType || 'HVAC'} service.

Based on our conversation, we will contact you within 24 hours to confirm the appointment details.

If you have any urgent concerns, please don't hesitate to call us back.

Best regards,
HVAC Service Team
      `.trim();

      await sendCustomerCommunication({
        customerId,
        userId: await this.getDefaultUserId(),
        channel: 'EMAIL',
        subject: 'Appointment Follow-up - HVAC Service',
        content: emailContent,
        direction: 'OUTBOUND',
      });
    } catch (error) {
      console.error('Error sending appointment follow-up:', error);
    }
  }

  /**
   * Create priority notification for team
   */
  private async createPriorityNotification(
    call: CallRecord,
    analysis: CallAnalysis,
    customerId: string
  ): Promise<void> {
    try {
      await prisma.notification.create({
        data: {
          userId: await this.getDefaultUserId(),
          type: 'CALL_PRIORITY',
          title: `${analysis.priority} Call: ${analysis.intent}`,
          message: `${analysis.emotion} call from ${call.from} - ${analysis.summary}`,
          data: {
            callId: call.id,
            customerId,
            analysis,
          },
          read: false,
        },
      });
    } catch (error) {
      console.error('Error creating priority notification for call:', error);
    }
  }

  /**
   * Update customer status
   */
  private async updateCustomerStatus(customerId: string, status: string): Promise<void> {
    try {
      await prisma.customer.update({
        where: { id: customerId },
        data: { status },
      });
    } catch (error) {
      console.error('Error updating customer status from call:', error);
    }
  }

  /**
   * Get default user ID
   */
  private async getDefaultUserId(): Promise<string> {
    const defaultUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' },
    });

    if (!defaultUser) {
      throw new Error('No default user found for call processing');
    }

    return defaultUser.id;
  }

  /**
   * Placeholder methods for other transcription providers
   */
  private async transcribeWithGoogle(recordingUrl: string): Promise<TranscriptionResult> {
    // Implement Google Speech-to-Text integration
    throw new Error('Google transcription not implemented yet');
  }

  private async transcribeWithAzure(recordingUrl: string): Promise<TranscriptionResult> {
    // Implement Azure Speech Services integration
    throw new Error('Azure transcription not implemented yet');
  }
}

// Export singleton instance
export const callTranscriptionService = new CallTranscriptionService();

// Export convenience functions
export const processCallRecording = (call: CallRecord) =>
  callTranscriptionService.processCallRecording(call);
