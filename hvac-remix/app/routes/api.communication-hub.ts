/**
 * Communication Hub API Routes
 * Provides endpoints for enhanced communication management
 * Part of FAZA 1: Komunikacja & Przetwarzanie Danych
 */

import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from '@remix-run/node';
import { 
  enhancedCommunicationHub,
  getUnifiedMessageFeed,
  getCommunicationInsights,
  sendUnifiedMessage,
  processIncomingMessage,
} from '~/services/enhanced-communication-hub.server';
import { requireUserId } from '~/session.server';

export async function loader({ request }: LoaderFunctionArgs) {
  await requireUserId(request);
  
  const url = new URL(request.url);
  const action = url.searchParams.get('action');
  const customerId = url.searchParams.get('customerId');

  try {
    switch (action) {
      case 'messages': {
        if (!customerId) {
          return json({ error: 'Customer ID required' }, { status: 400 });
        }

        const limit = parseInt(url.searchParams.get('limit') || '50');
        const offset = parseInt(url.searchParams.get('offset') || '0');
        const channels = url.searchParams.get('channels')?.split(',') || [];
        const unreadOnly = url.searchParams.get('unreadOnly') === 'true';

        const messages = await getUnifiedMessageFeed(customerId, {
          limit,
          offset,
          channels: channels.length > 0 ? channels : undefined,
          unreadOnly,
        });

        return json({ messages });
      }

      case 'insights': {
        const startDate = url.searchParams.get('startDate');
        const endDate = url.searchParams.get('endDate');

        if (!startDate || !endDate) {
          return json({ error: 'Start and end dates required' }, { status: 400 });
        }

        const timeRange = {
          start: new Date(startDate),
          end: new Date(endDate),
        };

        const insights = await getCommunicationInsights(timeRange, customerId || undefined);
        return json({ insights });
      }

      case 'channels': {
        const channels = enhancedCommunicationHub.getChannelStatus();
        return json({ channels });
      }

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Communication hub API error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  await requireUserId(request);

  const formData = await request.formData();
  const action = formData.get('action') as string;

  try {
    switch (action) {
      case 'send-unified-message': {
        const customerId = formData.get('customerId') as string;
        const channels = JSON.parse(formData.get('channels') as string);
        const content = JSON.parse(formData.get('content') as string);

        if (!customerId || !channels || !content) {
          return json({ error: 'Missing required fields' }, { status: 400 });
        }

        const result = await sendUnifiedMessage(customerId, channels, content);
        return json({ result });
      }

      case 'process-incoming-email': {
        const emailData = JSON.parse(formData.get('emailData') as string);
        
        const result = await processIncomingMessage('email', emailData);
        return json({ result });
      }

      case 'process-incoming-call': {
        const callData = JSON.parse(formData.get('callData') as string);
        
        const result = await processIncomingMessage('phone', callData);
        return json({ result });
      }

      case 'mark-messages-read': {
        const messageIds = JSON.parse(formData.get('messageIds') as string);
        
        await enhancedCommunicationHub.markMessagesAsRead(messageIds);
        return json({ success: true });
      }

      case 'update-channel-config': {
        const channelId = formData.get('channelId') as string;
        const config = JSON.parse(formData.get('config') as string);

        await enhancedCommunicationHub.updateChannelConfig(channelId, config);
        return json({ success: true });
      }

      case 'create-auto-response-template': {
        const template = JSON.parse(formData.get('template') as string);

        const templateId = await enhancedCommunicationHub.createAutoResponseTemplate(template);
        return json({ templateId });
      }

      default:
        return json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Communication hub action error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}
