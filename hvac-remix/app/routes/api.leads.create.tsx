import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { z } from "zod";
import { sendEmail } from "~/services/email.server";
import { sendSMS } from "~/services/sms.server";
import { createCustomer } from "~/services/customer.service";
import { fulmarkData, contactInfo } from "~/data/fulmark-data";

// Validation schema for lead creation
const leadSchema = z.object({
  name: z.string().min(2, "Imię musi mieć co najmniej 2 znaki"),
  phone: z.string().min(9, "Numer telefonu jest wymagany"),
  email: z.string().email("Nieprawidłowy adres email").optional().or(z.literal("")),
  location: z.string().optional(),
  service: z.enum(["montaz", "serwis", "naprawa", "wycena", "inne"]).optional(),
  message: z.string().optional(),
  source: z.string().default("website"),
});

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData);

    // Validate input data
    const validatedData = leadSchema.parse(data);

    // Create customer/lead in CRM system
    const customerData = {
      name: validatedData.name,
      phone: validatedData.phone,
      email: validatedData.email || undefined,
      address: validatedData.location || "",
      notes: `Lead ze strony internetowej - ${validatedData.service || "ogólne zapytanie"}\n\nWiadomość: ${validatedData.message || "Brak wiadomości"}`,
      source: validatedData.source,
      status: "lead",
      priority: getLeadPriority(validatedData.service),
      tags: [
        "website-lead",
        validatedData.service || "general",
        validatedData.source
      ].filter(Boolean)
    };

    // Create customer in GoBackend-Kratos
    const customer = await createCustomer(customerData);

    // Send notification email to Fulmark team
    await sendNotificationToTeam(validatedData, customer.id);

    // Send auto-response to customer
    if (validatedData.email) {
      await sendCustomerAutoResponse(validatedData);
    }

    // Send SMS notification to team for urgent requests
    if (isUrgentRequest(validatedData.service)) {
      await sendUrgentSMSNotification(validatedData);
    }

    return json({ 
      success: true, 
      message: "Zapytanie zostało wysłane. Skontaktujemy się w ciągu 2 godzin.",
      customerId: customer.id
    });

  } catch (error) {
    console.error("Error creating lead:", error);
    
    if (error instanceof z.ZodError) {
      return json({ 
        error: "Błąd walidacji danych: " + error.errors.map(e => e.message).join(", "),
        success: false 
      }, { status: 400 });
    }

    return json({ 
      error: "Wystąpił błąd podczas wysyłania zapytania. Spróbuj ponownie lub zadzwoń: " + contactInfo.phone,
      success: false 
    }, { status: 500 });
  }
}

function getLeadPriority(service?: string): "low" | "medium" | "high" | "urgent" {
  switch (service) {
    case "naprawa":
      return "urgent";
    case "serwis":
      return "high";
    case "montaz":
      return "medium";
    case "wycena":
      return "medium";
    default:
      return "low";
  }
}

function isUrgentRequest(service?: string): boolean {
  return service === "naprawa";
}

async function sendNotificationToTeam(leadData: any, customerId: string) {
  const serviceLabels = {
    montaz: "Montaż klimatyzacji",
    serwis: "Serwis klimatyzacji", 
    naprawa: "Naprawa klimatyzacji",
    wycena: "Bezpłatna wycena",
    inne: "Inne"
  };

  const emailContent = `
    <h2>🔥 Nowy lead ze strony internetowej!</h2>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Dane kontaktowe:</h3>
      <p><strong>Imię:</strong> ${leadData.name}</p>
      <p><strong>Telefon:</strong> ${leadData.phone}</p>
      <p><strong>Email:</strong> ${leadData.email || "Nie podano"}</p>
      <p><strong>Lokalizacja:</strong> ${leadData.location || "Nie podano"}</p>
    </div>

    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Szczegóły zapytania:</h3>
      <p><strong>Usługa:</strong> ${serviceLabels[leadData.service as keyof typeof serviceLabels] || "Nie wybrano"}</p>
      <p><strong>Priorytet:</strong> ${getLeadPriority(leadData.service).toUpperCase()}</p>
      <p><strong>Źródło:</strong> ${leadData.source}</p>
    </div>

    ${leadData.message ? `
    <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Wiadomość od klienta:</h3>
      <p style="font-style: italic;">"${leadData.message}"</p>
    </div>
    ` : ""}

    <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Następne kroki:</h3>
      <ul>
        <li>Skontaktuj się z klientem w ciągu 2 godzin</li>
        <li>Sprawdź dostępność w kalendarzu</li>
        <li>Przygotuj wstępną wycenę</li>
        <li>Zaktualizuj status w CRM (ID: ${customerId})</li>
      </ul>
    </div>

    <p style="color: #666; font-size: 12px;">
      Lead utworzony automatycznie przez system HVAC-Remix<br>
      Data: ${new Date().toLocaleString("pl-PL")}
    </p>
  `;

  await sendEmail({
    to: contactInfo.email,
    subject: `🔥 Nowy lead: ${leadData.name} - ${serviceLabels[leadData.service as keyof typeof serviceLabels] || "Ogólne zapytanie"}`,
    html: emailContent,
    priority: getLeadPriority(leadData.service) === "urgent" ? "high" : "normal"
  });
}

async function sendCustomerAutoResponse(leadData: any) {
  const serviceLabels = {
    montaz: "montażu klimatyzacji",
    serwis: "serwisu klimatyzacji", 
    naprawa: "naprawy klimatyzacji",
    wycena: "bezpłatnej wyceny",
    inne: "Państwa zapytania"
  };

  const emailContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">${fulmarkData.company.name}</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px;">${fulmarkData.company.tagline}</p>
      </div>

      <div style="padding: 30px; background: white;">
        <h2 style="color: #333; margin-bottom: 20px;">Dziękujemy za kontakt!</h2>
        
        <p>Szanowny/a <strong>${leadData.name}</strong>,</p>
        
        <p>Otrzymaliśmy Państwa zapytanie dotyczące <strong>${serviceLabels[leadData.service as keyof typeof serviceLabels] || "usług klimatyzacyjnych"}</strong>.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Co dalej?</h3>
          <ul style="color: #666;">
            <li>Skontaktujemy się z Państwem <strong>w ciągu 2 godzin</strong> w dni robocze</li>
            <li>Przeprowadzimy bezpłatną konsultację telefoniczną</li>
            <li>Umówimy wizytę w dogodnym terminie</li>
            <li>Przygotujemy szczegółową wycenę</li>
          </ul>
        </div>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Pilny kontakt?</h3>
          <p style="margin: 0; color: #666;">
            <strong>Telefon:</strong> ${contactInfo.phone}<br>
            <strong>Email:</strong> ${contactInfo.email}<br>
            <strong>Godziny pracy:</strong> Pon-Pt 8:00-18:00, Sob 9:00-15:00
          </p>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Dlaczego Fulmark?</h3>
          <ul style="color: #666;">
            <li><strong>Ponad 20 lat doświadczenia</strong> w branży klimatyzacyjnej</li>
            <li><strong>Autoryzowany serwis LG i Daikin</strong></li>
            <li><strong>Bezpłatna wycena</strong> i doradztwo techniczne</li>
            <li><strong>Gwarancja na montaż</strong> i serwis</li>
            <li><strong>Obsługa Warszawy i okolic</strong></li>
          </ul>
        </div>
      </div>

      <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
        <p style="margin: 0;">
          ${fulmarkData.company.fullName}<br>
          ${fulmarkData.company.experience} | Warszawa i okolice<br>
          Tel: ${contactInfo.phone} | Email: ${contactInfo.email}
        </p>
      </div>
    </div>
  `;

  await sendEmail({
    to: leadData.email,
    subject: `Potwierdzenie zapytania - ${fulmarkData.company.name}`,
    html: emailContent,
    replyTo: contactInfo.email
  });
}

async function sendUrgentSMSNotification(leadData: any) {
  const message = `🚨 PILNY LEAD - NAPRAWA KLIMATYZACJI
Klient: ${leadData.name}
Tel: ${leadData.phone}
Lokalizacja: ${leadData.location || "Nie podano"}
Skontaktuj się NATYCHMIAST!`;

  await sendSMS({
    to: contactInfo.phone,
    message: message
  });
}
