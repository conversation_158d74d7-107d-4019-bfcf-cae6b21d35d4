// 📈 Forecasting Route - AI-Powered Sales Forecasting Interface
// Claude 4 w Augment Framework - DIVINE INTELLIGENCE! 🧠

import type { LoaderFunctionArgs, ActionFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useActionData, useFetcher } from "@remix-run/react";
import { useState } from "react";
import { ForecastingDashboard } from "~/components/opportunities/ForecastingDashboard";
import { ForecastingService } from "~/services/forecasting.service";
import { requireUserId } from "~/session.server";
import { getSEOConfig } from "~/components/seo/SEOHead";
import type { ForecastEntry, ForecastAnalytics, ForecastPeriod, AIModel } from "~/models/forecasting";

export const meta: MetaFunction = () => getSEOConfig("forecasting", {
  title: "Sales Forecasting - AI-Powered Revenue Predictions",
  description: "Advanced sales forecasting with AI-powered predictions, scenario analysis, and comprehensive analytics for HVAC business.",
  keywords: "sales forecasting, ai predictions, revenue analytics, business intelligence, hvac forecasting"
});

interface LoaderData {
  forecasts: ForecastEntry[];
  analytics: ForecastAnalytics | null;
  availableModels: AIModel[];
  forecastConfig: any;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const url = new URL(request.url);
  
  // Parse query parameters
  const period = url.searchParams.get('period') as ForecastPeriod || 'monthly';
  const model = url.searchParams.get('model') as AIModel || 'bielik-v3';
  const startDate = url.searchParams.get('start') 
    ? new Date(url.searchParams.get('start')!) 
    : new Date();
  const endDate = url.searchParams.get('end') 
    ? new Date(url.searchParams.get('end')!) 
    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

  try {
    // Get existing forecasts
    const forecasts = await ForecastingService.list({
      periodType: period,
      limit: 50
    });

    // Generate analytics for the latest forecast
    let analytics = null;
    if (forecasts.length > 0) {
      const latestForecast = forecasts[0];
      analytics = await ForecastingService.generateForecastAnalytics(latestForecast.id);
    }

    // Available AI models
    const availableModels: AIModel[] = ['bielik-v3', 'gemma3-4b', 'ensemble'];

    // Forecast configuration
    const forecastConfig = await ForecastingService.getForecastConfig();

    return json<LoaderData>({
      forecasts,
      analytics,
      availableModels,
      forecastConfig
    });
  } catch (error) {
    console.error("Error loading forecasting data:", error);
    throw new Response("Failed to load forecasting data", { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    switch (action) {
      case "generate_forecast": {
        const periodStart = new Date(formData.get("periodStart") as string);
        const periodEnd = new Date(formData.get("periodEnd") as string);
        const model = formData.get("model") as AIModel || 'bielik-v3';
        const includeScenarios = formData.get("includeScenarios") === "true";
        const includeBreakdowns = formData.get("includeBreakdowns") === "true";

        if (!periodStart || !periodEnd) {
          return json({ error: "Invalid date range" }, { status: 400 });
        }

        const forecast = await ForecastingService.generateForecast(
          periodStart,
          periodEnd,
          {
            model,
            includeScenarios,
            includeBreakdowns,
            confidenceLevel: 95
          }
        );

        return json({ 
          success: true, 
          forecast,
          message: "Forecast generated successfully" 
        });
      }

      case "refresh_forecasts": {
        // Generate new forecasts for the next 3 months
        const now = new Date();
        const forecasts = [];

        for (let i = 0; i < 3; i++) {
          const periodStart = new Date(now.getFullYear(), now.getMonth() + i, 1);
          const periodEnd = new Date(now.getFullYear(), now.getMonth() + i + 1, 0);

          try {
            const forecast = await ForecastingService.generateForecast(
              periodStart,
              periodEnd,
              {
                model: 'bielik-v3',
                includeScenarios: true,
                includeBreakdowns: true
              }
            );
            forecasts.push(forecast);
          } catch (error) {
            console.error(`Error generating forecast for period ${i}:`, error);
          }
        }

        return json({ 
          success: true, 
          forecasts,
          message: `Generated ${forecasts.length} new forecasts` 
        });
      }

      case "update_forecast": {
        const forecastId = formData.get("forecastId") as string;
        const updates = JSON.parse(formData.get("updates") as string);

        if (!forecastId) {
          return json({ error: "Forecast ID required" }, { status: 400 });
        }

        const updatedForecast = await ForecastingService.update(forecastId, updates);

        return json({ 
          success: true, 
          forecast: updatedForecast,
          message: "Forecast updated successfully" 
        });
      }

      case "delete_forecast": {
        const forecastId = formData.get("forecastId") as string;

        if (!forecastId) {
          return json({ error: "Forecast ID required" }, { status: 400 });
        }

        await ForecastingService.delete(forecastId);

        return json({ 
          success: true, 
          message: "Forecast deleted successfully" 
        });
      }

      case "export_forecasts": {
        const format = formData.get("format") as string || 'csv';
        const forecastIds = JSON.parse(formData.get("forecastIds") as string || '[]');

        const exportData = await ForecastingService.exportForecasts(forecastIds, format);

        return json({ 
          success: true, 
          exportData,
          message: "Forecasts exported successfully" 
        });
      }

      case "generate_analytics": {
        const forecastId = formData.get("forecastId") as string;

        if (!forecastId) {
          return json({ error: "Forecast ID required" }, { status: 400 });
        }

        const analytics = await ForecastingService.generateForecastAnalytics(forecastId);

        return json({ 
          success: true, 
          analytics,
          message: "Analytics generated successfully" 
        });
      }

      case "create_scenario": {
        const forecastId = formData.get("forecastId") as string;
        const scenarioData = JSON.parse(formData.get("scenarioData") as string);

        if (!forecastId || !scenarioData) {
          return json({ error: "Missing required data" }, { status: 400 });
        }

        const scenario = await ForecastingService.createScenario(forecastId, scenarioData);

        return json({ 
          success: true, 
          scenario,
          message: "Scenario created successfully" 
        });
      }

      case "validate_model": {
        const model = formData.get("model") as AIModel;
        const testPeriod = parseInt(formData.get("testPeriod") as string) || 6;

        if (!model) {
          return json({ error: "Model required" }, { status: 400 });
        }

        const validation = await ForecastingService.validateModel(model, testPeriod);

        return json({ 
          success: true, 
          validation,
          message: "Model validation completed" 
        });
      }

      default:
        return json({ error: "Unknown action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Forecasting action error:", error);
    return json({ 
      error: "Failed to perform action",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export default function OpportunitiesForecasting() {
  const { forecasts, analytics, availableModels, forecastConfig } = useLoaderData<LoaderData>();
  const actionData = useActionData<any>();
  const fetcher = useFetcher();
  const [selectedModel, setSelectedModel] = useState<AIModel>('bielik-v3');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateNewForecast = () => {
    setIsGenerating(true);
    
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    const endOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 2, 0);

    fetcher.submit(
      {
        action: "generate_forecast",
        periodStart: nextMonth.toISOString(),
        periodEnd: endOfNextMonth.toISOString(),
        model: selectedModel,
        includeScenarios: "true",
        includeBreakdowns: "true"
      },
      { method: "post" }
    );

    setTimeout(() => setIsGenerating(false), 3000);
  };

  const refreshAllForecasts = () => {
    fetcher.submit(
      { action: "refresh_forecasts" },
      { method: "post" }
    );
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Quick Actions */}
        <div className="mb-8 flex flex-wrap gap-4">
          <div className="flex items-center gap-3">
            <select 
              value={selectedModel} 
              onChange={(e) => setSelectedModel(e.target.value as AIModel)}
              className="bg-slate-800 border border-slate-700 text-white rounded-lg px-3 py-2"
            >
              {availableModels.map(model => (
                <option key={model} value={model}>
                  {model.toUpperCase()}
                </option>
              ))}
            </select>
            
            <button
              onClick={generateNewForecast}
              disabled={isGenerating}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold rounded-lg transition-all duration-300 hover:scale-105 disabled:opacity-50"
            >
              {isGenerating ? 'Generating...' : 'Generate New Forecast'}
            </button>
            
            <button
              onClick={refreshAllForecasts}
              className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold rounded-lg transition-all duration-300 hover:scale-105"
            >
              Refresh All
            </button>
          </div>
        </div>

        {/* Forecasting Dashboard */}
        <ForecastingDashboard 
          forecasts={forecasts}
          analytics={analytics}
        />

        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="fixed bottom-4 right-4 bg-green-500/20 border border-green-500/30 text-green-400 px-4 py-2 rounded-lg">
            {actionData.message}
          </div>
        )}

        {actionData?.error && (
          <div className="fixed bottom-4 right-4 bg-red-500/20 border border-red-500/30 text-red-400 px-4 py-2 rounded-lg">
            {actionData.error}
          </div>
        )}
      </div>
    </main>
  );
}
