import type { MetaFunction } from "@remix-run/node";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Shield,
  Star,
  CheckCircle,
  Wrench,
  Calendar,
  MessageSquare
} from "lucide-react";
import { ContactForm } from "~/components/website/ContactForm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { ServiceAreaMap } from "~/components/fulmark/ServiceAreaMap";
import { fulmarkData, contactInfo } from "~/data/fulmark-data";

export const meta: MetaFunction = () => [
  { title: "Kontakt - Fulmark Klimatyzacja | Warszawa i okolice" },
  { 
    name: "description", 
    content: "Skontaktuj się z Fulmark - profesjonalna klimatyzacja w Warszawie. Bezpłatna wycena, montaż LG i Daikin. Tel: +48 600 100 901. Ponad 20 lat doświadczenia." 
  },
  { name: "keywords", content: "kontakt fulmark, klimatyzacja warszawa, monta<PERSON> klimatyzacji, ser<PERSON><PERSON> klim<PERSON>, lg daikin" },
  { property: "og:title", content: "Kontakt - Fulmark Klimatyzacja" },
  { property: "og:description", content: "Skontaktuj się z ekspertami klimatyzacji. Bezpłatna wycena i doradztwo." },
  { property: "og:type", content: "website" },
];

export default function KontaktPage() {
  const workingHours = [
    { day: "Poniedziałek - Piątek", hours: "8:00 - 18:00", available: true },
    { day: "Sobota", hours: "9:00 - 15:00", available: true },
    { day: "Niedziela", hours: "Zamknięte", available: false }
  ];

  const contactMethods = [
    {
      icon: Phone,
      title: "Telefon",
      value: contactInfo.phone,
      description: "Zadzwoń w godzinach pracy",
      action: `tel:${contactInfo.phone}`,
      color: "text-green-400",
      bgColor: "bg-green-500/20"
    },
    {
      icon: Mail,
      title: "Email",
      value: contactInfo.email,
      description: "Odpowiadamy w ciągu 2 godzin",
      action: `mailto:${contactInfo.email}`,
      color: "text-blue-400",
      bgColor: "bg-blue-500/20"
    },
    {
      icon: MessageSquare,
      title: "Formularz kontaktowy",
      value: "Online 24/7",
      description: "Najszybszy sposób kontaktu",
      action: "#contact-form",
      color: "text-purple-400",
      bgColor: "bg-purple-500/20"
    }
  ];

  const services = [
    {
      icon: Wrench,
      title: "Montaż klimatyzacji",
      description: "Profesjonalny montaż z gwarancją",
      features: ["Bezpłatna wycena", "Montaż w 1 dzień", "Gwarancja na montaż"]
    },
    {
      icon: Calendar,
      title: "Serwis klimatyzacji", 
      description: "Przeglądy i konserwacja",
      features: ["Przeglądy okresowe", "Czyszczenie", "Uzupełnianie czynnika"]
    },
    {
      icon: Shield,
      title: "Naprawa klimatyzacji",
      description: "Szybka diagnostyka i naprawa",
      features: ["Dojazd w 24h", "Oryginalne części", "Gwarancja na naprawę"]
    }
  ];

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-black bg-gradient-to-r from-blue-400 via-green-400 to-blue-600 bg-clip-text text-transparent mb-6">
            Skontaktuj się z nami
          </h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Jesteśmy do Państwa dyspozycji. Bezpłatna wycena, profesjonalne doradztwo 
            i szybka realizacja zleceń w Warszawie i okolicach.
          </p>
        </div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          {contactMethods.map((method, index) => {
            const Icon = method.icon;
            return (
              <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:scale-105 transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className={`inline-flex p-4 rounded-full ${method.bgColor} mb-4`}>
                    <Icon className={`h-8 w-8 ${method.color}`} />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">{method.title}</h3>
                  <p className={`text-lg font-medium ${method.color} mb-2`}>{method.value}</p>
                  <p className="text-slate-400 text-sm mb-4">{method.description}</p>
                  <a 
                    href={method.action}
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-lg ${method.bgColor} ${method.color} hover:scale-105 transition-all duration-300`}
                  >
                    Skontaktuj się
                  </a>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Company Info & Hours */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {/* Company Info */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Star className="h-6 w-6 text-yellow-400" />
                {fulmarkData.company.fullName}
              </CardTitle>
              <CardDescription className="text-slate-300 text-lg">
                {fulmarkData.company.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-400">{fulmarkData.stats.experience}</div>
                  <div className="text-sm text-slate-400">lat doświadczenia</div>
                </div>
                <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">{fulmarkData.stats.completedProjects}</div>
                  <div className="text-sm text-slate-400">projektów</div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-white">Certyfikaty i uprawnienia:</h4>
                {fulmarkData.certifications.map((cert, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span className="text-slate-300">{cert}</span>
                  </div>
                ))}
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-white">Obsługujemy marki:</h4>
                <div className="flex gap-4">
                  {fulmarkData.brands.map((brand, index) => (
                    <div key={index} className="px-4 py-2 bg-slate-700/30 rounded-lg">
                      <span className="font-medium text-white">{brand.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Working Hours */}
          <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-white flex items-center gap-3">
                <Clock className="h-6 w-6 text-blue-400" />
                Godziny pracy
              </CardTitle>
              <CardDescription className="text-slate-300">
                Jesteśmy dostępni przez większość tygodnia
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {workingHours.map((schedule, index) => (
                <div key={index} className={`flex justify-between items-center p-3 rounded-lg ${
                  schedule.available ? 'bg-green-500/10 border border-green-500/30' : 'bg-slate-700/30'
                }`}>
                  <span className="font-medium text-white">{schedule.day}</span>
                  <span className={`font-medium ${schedule.available ? 'text-green-400' : 'text-slate-400'}`}>
                    {schedule.hours}
                  </span>
                </div>
              ))}

              <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-4 mt-6">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-5 w-5 text-orange-400" />
                  <span className="font-medium text-orange-400">Serwis awaryjny</span>
                </div>
                <p className="text-sm text-orange-300">
                  {contactInfo.emergencyService.hours} - dostępny w sezonie letnim
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Services Overview */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Nasze usługi</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {services.map((service, index) => {
              const Icon = service.icon;
              return (
                <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:scale-105 transition-all duration-300">
                  <CardHeader>
                    <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
                      <Icon className="h-6 w-6 text-blue-400" />
                      {service.title}
                    </CardTitle>
                    <CardDescription className="text-slate-300">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                          <span className="text-slate-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Service Area Map */}
        <div className="mb-16">
          <ServiceAreaMap />
        </div>

        {/* Contact Form */}
        <div id="contact-form">
          <ContactForm source="contact-page" />
        </div>
      </div>
    </main>
  );
}
