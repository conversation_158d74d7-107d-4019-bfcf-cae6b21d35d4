import type { MetaFunction } from "@remix-run/node";
import { 
  Calculator, 
  TrendingUp, 
  DollarSign,
  Info,
  CheckCircle,
  Star,
  Thermometer,
  Zap,
  Home,
  Building
} from "lucide-react";
import { CostCalculator } from "~/components/website/CostCalculator";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { fulmarkData } from "~/data/fulmark-data";

export const meta: MetaFunction = () => [
  { title: "Kalkulator kosztów klimatyzacji - Fulmark | Warszawa" },
  { 
    name: "description", 
    content: "Sprawdź orientacyjny koszt montażu klimatyzacji. Kalkulator uwzględnia typ budynku, powier<PERSON>chnię, markę i poziom skomplikowania. Bezpłatna wycena." 
  },
  { name: "keywords", content: "kalkulator kosztów klimatyzacji, cena montażu klimatyzacji, koszt klimatyzacji warszawa, lg daikin cena" },
  { property: "og:title", content: "Kalkulator kosztów klimatyzacji - Fulmark" },
  { property: "og:description", content: "Sprawdź orientacyjny koszt montażu klimatyzacji w Twoim domu." },
  { property: "og:type", content: "website" },
];

export default function KalkulatorPage() {
  const priceFactors = [
    {
      icon: Home,
      title: "Typ budynku",
      description: "Mieszkanie, dom czy biuro - każdy typ ma inne wymagania",
      impact: "Wpływ: 0-50% ceny"
    },
    {
      icon: Thermometer,
      title: "Powierzchnia",
      description: "Większe pomieszczenia wymagają mocniejszych urządzeń",
      impact: "Wpływ: 20-100% ceny"
    },
    {
      icon: Building,
      title: "Liczba pomieszczeń",
      description: "System multi split dla wielu pomieszczeń",
      impact: "Wpływ: 80-220% ceny"
    },
    {
      icon: Star,
      title: "Marka urządzenia",
      description: "LG i Daikin - różne poziomy cenowe",
      impact: "Wpływ: 10-30% ceny"
    },
    {
      icon: Zap,
      title: "Skomplikowanie montażu",
      description: "Dostępność, długość tras, dodatkowe prace",
      impact: "Wpływ: 0-60% ceny"
    }
  ];

  const priceRanges = [
    {
      type: "Klimatyzator ścienny",
      room: "1 pomieszczenie (20-30m²)",
      lgPrice: "2.500 - 4.500 zł",
      daikinPrice: "3.000 - 5.200 zł",
      description: "Najpopularniejsze rozwiązanie do domów i mieszkań"
    },
    {
      type: "System multi split",
      room: "2-3 pomieszczenia",
      lgPrice: "8.000 - 15.000 zł", 
      daikinPrice: "9.500 - 18.000 zł",
      description: "Jedno urządzenie zewnętrzne, kilka wewnętrznych"
    },
    {
      type: "Klimatyzator kanałowy",
      room: "Dom/mieszkanie (60-120m²)",
      lgPrice: "12.000 - 25.000 zł",
      daikinPrice: "15.000 - 30.000 zł", 
      description: "Niewidoczne rozwiązanie z rozprowadzeniem kanałami"
    },
    {
      type: "System kasetonowy",
      room: "Biuro/lokal (40-80m²)",
      lgPrice: "6.000 - 12.000 zł",
      daikinPrice: "7.500 - 15.000 zł",
      description: "Idealne do przestrzeni komercyjnych"
    }
  ];

  const calculatorBenefits = [
    {
      icon: Calculator,
      title: "Szybka wycena",
      description: "Otrzymaj orientacyjny koszt w kilka minut"
    },
    {
      icon: TrendingUp,
      title: "Precyzyjne obliczenia",
      description: "Uwzględniamy wszystkie istotne czynniki"
    },
    {
      icon: DollarSign,
      title: "Przejrzyste ceny",
      description: "Bez ukrytych kosztów i niespodzianek"
    },
    {
      icon: CheckCircle,
      title: "Bezpłatna wycena",
      description: "Dokładna wycena po wizji lokalnej - gratis"
    }
  ];

  const importantNotes = [
    "Ceny zawierają koszt urządzenia i standardowego montażu",
    "Dokładny koszt ustalamy po bezpłatnej wizji lokalnej",
    "Cena może się różnić w zależności od dostępności i specyfiki obiektu",
    "Oferujemy możliwość płatności ratalnej",
    "Wszystkie ceny zawierają gwarancję na montaż"
  ];

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-black bg-gradient-to-r from-blue-400 via-green-400 to-blue-600 bg-clip-text text-transparent mb-6">
            Kalkulator kosztów klimatyzacji
          </h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Sprawdź orientacyjny koszt montażu klimatyzacji w Twoim domu lub biurze. 
            Kalkulator uwzględnia wszystkie istotne czynniki wpływające na cenę.
          </p>
        </div>

        {/* Calculator Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {calculatorBenefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:scale-105 transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="inline-flex p-4 rounded-full bg-green-500/20 mb-4">
                    <Icon className="h-8 w-8 text-green-400" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">{benefit.title}</h3>
                  <p className="text-slate-300 text-sm">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Price Factors */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Co wpływa na cenę?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {priceFactors.map((factor, index) => {
              const Icon = factor.icon;
              return (
                <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
                  <CardHeader>
                    <CardTitle className="text-lg font-bold text-white flex items-center gap-3">
                      <Icon className="h-6 w-6 text-blue-400" />
                      {factor.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-slate-300 mb-3">{factor.description}</p>
                    <div className="text-sm text-blue-400 font-medium">{factor.impact}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Price Ranges */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Orientacyjne ceny</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {priceRanges.map((range, index) => (
              <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-white">{range.type}</CardTitle>
                  <CardDescription className="text-slate-300">
                    {range.room}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-slate-300 text-sm">{range.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 bg-slate-700/30 rounded-lg">
                      <div className="text-sm text-slate-400 mb-1">LG</div>
                      <div className="font-bold text-blue-400">{range.lgPrice}</div>
                    </div>
                    <div className="p-3 bg-slate-700/30 rounded-lg">
                      <div className="text-sm text-slate-400 mb-1">Daikin</div>
                      <div className="font-bold text-green-400">{range.daikinPrice}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Important Information */}
        <div className="mb-16">
          <Card className="bg-blue-500/10 border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-blue-400 flex items-center gap-3">
                <Info className="h-6 w-6" />
                Ważne informacje o cenach
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {importantNotes.map((note, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <span className="text-slate-300">{note}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Company Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 bg-slate-800/30 rounded-lg">
            <div className="text-3xl font-bold text-yellow-400 mb-2">{fulmarkData.stats.experience}</div>
            <div className="text-slate-400">lat doświadczenia</div>
          </div>
          <div className="text-center p-6 bg-slate-800/30 rounded-lg">
            <div className="text-3xl font-bold text-green-400 mb-2">{fulmarkData.stats.completedProjects}</div>
            <div className="text-slate-400">projektów</div>
          </div>
          <div className="text-center p-6 bg-slate-800/30 rounded-lg">
            <div className="text-3xl font-bold text-blue-400 mb-2">{fulmarkData.stats.satisfiedCustomers}</div>
            <div className="text-slate-400">zadowolonych klientów</div>
          </div>
          <div className="text-center p-6 bg-slate-800/30 rounded-lg">
            <div className="text-3xl font-bold text-purple-400 mb-2">2</div>
            <div className="text-slate-400">marki premium</div>
          </div>
        </div>

        {/* Cost Calculator */}
        <CostCalculator />
      </div>
    </main>
  );
}
