// 🎯 Opportunities Pipeline Route - Sales Pipeline Management
// Claude 4 w Augment Framework - DIVINE QUALITY! 💎

import type { LoaderFunctionArgs, ActionFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData, useActionData, useFetcher } from "@remix-run/react";
import { useState } from "react";
import { PipelineBoard } from "~/components/opportunities/PipelineBoard";
import { OpportunityService } from "~/services/opportunity.service";
import { requireUserId } from "~/session.server";
import { getSEOConfig } from "~/components/seo/SEOHead";
import type { Opportunity, OpportunityStage } from "~/models/opportunity";

export const meta: MetaFunction = () => getSEOConfig("pipeline", {
  title: "Sales Pipeline - Opportunities Management",
  description: "Manage your HVAC sales pipeline with AI-powered insights and drag-and-drop functionality.",
  keywords: "sales pipeline, opportunities, crm, hvac sales, lead management"
});

interface LoaderData {
  opportunities: Opportunity[];
  pipelineStats: PipelineStats;
  stages: PipelineStageConfig[];
}

interface PipelineStats {
  totalValue: number;
  weightedValue: number;
  totalCount: number;
  conversionRate: number;
  averageDealSize: number;
  averageSalesCycle: number;
}

interface PipelineStageConfig {
  stage: OpportunityStage;
  name: string;
  color: string;
  count: number;
  value: number;
  order: number;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  
  try {
    // Get opportunities with AI insights
    const opportunities = await OpportunityService.list({
      limit: 1000 // Get all active opportunities
    });

    // Calculate pipeline statistics
    const pipelineStats = calculatePipelineStats(opportunities);
    
    // Configure pipeline stages
    const stages = configurePipelineStages(opportunities);

    return json<LoaderData>({
      opportunities,
      pipelineStats,
      stages
    });
  } catch (error) {
    console.error("Error loading pipeline data:", error);
    throw new Response("Failed to load pipeline data", { status: 500 });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const formData = await request.formData();
  const action = formData.get("action");

  try {
    switch (action) {
      case "move_opportunity": {
        const opportunityId = formData.get("opportunityId") as string;
        const newStage = formData.get("newStage") as OpportunityStage;
        
        if (!opportunityId || !newStage) {
          return json({ error: "Missing required fields" }, { status: 400 });
        }

        const updatedOpportunity = await OpportunityService.update(opportunityId, {
          stage: newStage,
          updatedAt: new Date()
        });

        return json({ 
          success: true, 
          opportunity: updatedOpportunity,
          message: `Opportunity moved to ${newStage}` 
        });
      }

      case "update_probability": {
        const opportunityId = formData.get("opportunityId") as string;
        const probability = parseInt(formData.get("probability") as string);
        
        if (!opportunityId || isNaN(probability)) {
          return json({ error: "Invalid data" }, { status: 400 });
        }

        const updatedOpportunity = await OpportunityService.update(opportunityId, {
          probability: Math.max(0, Math.min(100, probability))
        });

        return json({ 
          success: true, 
          opportunity: updatedOpportunity,
          message: "Probability updated" 
        });
      }

      case "bulk_update": {
        const opportunityIds = JSON.parse(formData.get("opportunityIds") as string);
        const updates = JSON.parse(formData.get("updates") as string);
        
        const results = await Promise.all(
          opportunityIds.map((id: string) => 
            OpportunityService.update(id, updates)
          )
        );

        return json({ 
          success: true, 
          count: results.length,
          message: `Updated ${results.length} opportunities` 
        });
      }

      default:
        return json({ error: "Unknown action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Pipeline action error:", error);
    return json({ 
      error: "Failed to perform action",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

export default function OpportunitiesPipeline() {
  const { opportunities, pipelineStats, stages } = useLoaderData<LoaderData>();
  const actionData = useActionData<any>();
  const fetcher = useFetcher();
  const [selectedOpportunity, setSelectedOpportunity] = useState<Opportunity | null>(null);

  const handleOpportunityMove = (opportunityId: string, newStage: OpportunityStage) => {
    fetcher.submit(
      {
        action: "move_opportunity",
        opportunityId,
        newStage
      },
      { method: "post" }
    );
  };

  const handleOpportunityClick = (opportunity: Opportunity) => {
    setSelectedOpportunity(opportunity);
    // Navigate to opportunity detail or open modal
    window.location.href = `/opportunities/${opportunity.id}`;
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Pipeline Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Total Pipeline</div>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(pipelineStats.totalValue)}
            </div>
          </div>
          
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Weighted Value</div>
            <div className="text-2xl font-bold text-green-400">
              {formatCurrency(pipelineStats.weightedValue)}
            </div>
          </div>
          
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Opportunities</div>
            <div className="text-2xl font-bold text-blue-400">
              {pipelineStats.totalCount}
            </div>
          </div>
          
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Win Rate</div>
            <div className="text-2xl font-bold text-purple-400">
              {pipelineStats.conversionRate.toFixed(1)}%
            </div>
          </div>
          
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Avg Deal Size</div>
            <div className="text-2xl font-bold text-yellow-400">
              {formatCurrency(pipelineStats.averageDealSize)}
            </div>
          </div>
          
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-4">
            <div className="text-sm text-slate-400">Avg Cycle</div>
            <div className="text-2xl font-bold text-orange-400">
              {pipelineStats.averageSalesCycle} days
            </div>
          </div>
        </div>

        {/* Pipeline Board */}
        <PipelineBoard
          opportunities={opportunities}
          stages={stages}
          onOpportunityMove={handleOpportunityMove}
          onOpportunityClick={handleOpportunityClick}
        />

        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="fixed bottom-4 right-4 bg-green-500/20 border border-green-500/30 text-green-400 px-4 py-2 rounded-lg">
            {actionData.message}
          </div>
        )}

        {actionData?.error && (
          <div className="fixed bottom-4 right-4 bg-red-500/20 border border-red-500/30 text-red-400 px-4 py-2 rounded-lg">
            {actionData.error}
          </div>
        )}
      </div>
    </main>
  );
}

// Helper Functions

function calculatePipelineStats(opportunities: Opportunity[]): PipelineStats {
  const activeOpportunities = opportunities.filter(opp => 
    opp.status === 'active' && 
    !['closed_won', 'closed_lost'].includes(opp.stage)
  );

  const totalValue = activeOpportunities.reduce((sum, opp) => sum + opp.value, 0);
  const weightedValue = activeOpportunities.reduce((sum, opp) => 
    sum + (opp.value * (opp.probability / 100)), 0
  );

  const closedOpportunities = opportunities.filter(opp => 
    ['closed_won', 'closed_lost'].includes(opp.stage)
  );
  const wonOpportunities = closedOpportunities.filter(opp => opp.stage === 'closed_won');
  
  const conversionRate = closedOpportunities.length > 0 
    ? (wonOpportunities.length / closedOpportunities.length) * 100 
    : 0;

  const averageDealSize = wonOpportunities.length > 0
    ? wonOpportunities.reduce((sum, opp) => sum + opp.value, 0) / wonOpportunities.length
    : 0;

  const averageSalesCycle = wonOpportunities.length > 0
    ? wonOpportunities.reduce((sum, opp) => {
        const cycleTime = opp.actualCloseDate && opp.createdAt
          ? Math.floor((opp.actualCloseDate.getTime() - opp.createdAt.getTime()) / (1000 * 60 * 60 * 24))
          : 0;
        return sum + cycleTime;
      }, 0) / wonOpportunities.length
    : 0;

  return {
    totalValue,
    weightedValue,
    totalCount: activeOpportunities.length,
    conversionRate,
    averageDealSize,
    averageSalesCycle
  };
}

function configurePipelineStages(opportunities: Opportunity[]): PipelineStageConfig[] {
  const stageConfigs = [
    { stage: 'lead' as OpportunityStage, name: 'Lead', color: '#64748b', order: 1 },
    { stage: 'qualified' as OpportunityStage, name: 'Qualified', color: '#3b82f6', order: 2 },
    { stage: 'needs_analysis' as OpportunityStage, name: 'Needs Analysis', color: '#8b5cf6', order: 3 },
    { stage: 'proposal' as OpportunityStage, name: 'Proposal', color: '#f59e0b', order: 4 },
    { stage: 'negotiation' as OpportunityStage, name: 'Negotiation', color: '#10b981', order: 5 },
    { stage: 'closed_won' as OpportunityStage, name: 'Closed Won', color: '#22c55e', order: 6 }
  ];

  return stageConfigs.map(config => {
    const stageOpportunities = opportunities.filter(opp => opp.stage === config.stage);
    const stageValue = stageOpportunities.reduce((sum, opp) => sum + opp.value, 0);

    return {
      ...config,
      count: stageOpportunities.length,
      value: stageValue
    };
  });
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: 'PLN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}
