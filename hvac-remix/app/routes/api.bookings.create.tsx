import type { ActionFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { z } from "zod";
import { sendEmail } from "~/services/email.server";
import { sendSMS } from "~/services/sms.server";
import { createCustomer } from "~/services/customer.service";
import { createServiceOrder } from "~/services/service-order.service";
import { fulmarkData, contactInfo } from "~/data/fulmark-data";

// Validation schema for booking creation
const bookingSchema = z.object({
  service: z.enum(["montaz", "serwis", "naprawa", "wycena"]),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Nieprawidłowy format daty"),
  timeSlot: z.string(),
  name: z.string().min(2, "Imię musi mieć co najmniej 2 znaki"),
  phone: z.string().min(9, "Numer telefonu jest wymagany"),
  email: z.string().email("Nieprawidłowy adres email").optional().or(z.literal("")),
  address: z.string().min(5, "Adres jest wymagany"),
  notes: z.string().optional(),
});

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const data = Object.fromEntries(formData);

    // Validate input data
    const validatedData = bookingSchema.parse(data);

    // Validate date is in the future and not Sunday
    const bookingDate = new Date(validatedData.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (bookingDate < today) {
      return json({ 
        error: "Nie można zarezerwować wizyty w przeszłości",
        success: false 
      }, { status: 400 });
    }

    if (bookingDate.getDay() === 0) {
      return json({ 
        error: "Nie pracujemy w niedziele. Wybierz inny dzień.",
        success: false 
      }, { status: 400 });
    }

    // Create or find customer
    const customerData = {
      name: validatedData.name,
      phone: validatedData.phone,
      email: validatedData.email || undefined,
      address: validatedData.address,
      notes: `Klient z rezerwacji online - ${getServiceLabel(validatedData.service)}`,
      source: "website-booking",
      status: "active",
      priority: getBookingPriority(validatedData.service),
      tags: ["website-booking", validatedData.service]
    };

    const customer = await createCustomer(customerData);

    // Create service order
    const serviceOrderData = {
      customerId: customer.id,
      title: `${getServiceLabel(validatedData.service)} - ${validatedData.name}`,
      description: `Rezerwacja online na ${formatDate(bookingDate)} o ${validatedData.timeSlot}\n\nAdres: ${validatedData.address}\n\nUwagi: ${validatedData.notes || "Brak"}`,
      serviceType: validatedData.service,
      status: "scheduled",
      priority: getBookingPriority(validatedData.service),
      scheduledDate: `${validatedData.date}T${validatedData.timeSlot}:00`,
      estimatedDuration: getServiceDuration(validatedData.service),
      address: validatedData.address,
      source: "website-booking"
    };

    const serviceOrder = await createServiceOrder(serviceOrderData);

    // Send confirmation email to customer
    if (validatedData.email) {
      await sendBookingConfirmationToCustomer(validatedData, serviceOrder.id);
    }

    // Send notification to team
    await sendBookingNotificationToTeam(validatedData, customer.id, serviceOrder.id);

    // Send SMS for urgent bookings
    if (validatedData.service === "naprawa") {
      await sendUrgentBookingSMS(validatedData);
    }

    return json({ 
      success: true, 
      message: "Wizyta została zarezerwowana. Potwierdzimy termin telefonicznie w ciągu godziny.",
      bookingId: serviceOrder.id,
      customerId: customer.id
    });

  } catch (error) {
    console.error("Error creating booking:", error);
    
    if (error instanceof z.ZodError) {
      return json({ 
        error: "Błąd walidacji danych: " + error.errors.map(e => e.message).join(", "),
        success: false 
      }, { status: 400 });
    }

    return json({ 
      error: "Wystąpił błąd podczas rezerwacji. Spróbuj ponownie lub zadzwoń: " + contactInfo.phone,
      success: false 
    }, { status: 500 });
  }
}

function getServiceLabel(service: string): string {
  const labels = {
    montaz: "Montaż klimatyzacji",
    serwis: "Serwis klimatyzacji",
    naprawa: "Naprawa klimatyzacji",
    wycena: "Bezpłatna wycena"
  };
  return labels[service as keyof typeof labels] || service;
}

function getBookingPriority(service: string): "low" | "medium" | "high" | "urgent" {
  switch (service) {
    case "naprawa":
      return "urgent";
    case "serwis":
      return "high";
    case "montaz":
      return "medium";
    case "wycena":
      return "medium";
    default:
      return "low";
  }
}

function getServiceDuration(service: string): number {
  const durations = {
    montaz: 240, // 4 hours
    serwis: 120, // 2 hours
    naprawa: 180, // 3 hours
    wycena: 60   // 1 hour
  };
  return durations[service as keyof typeof durations] || 120;
}

function formatDate(date: Date): string {
  return date.toLocaleDateString("pl-PL", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric"
  });
}

async function sendBookingConfirmationToCustomer(bookingData: any, serviceOrderId: string) {
  const serviceLabels = {
    montaz: "Montaż klimatyzacji",
    serwis: "Serwis klimatyzacji",
    naprawa: "Naprawa klimatyzacji",
    wycena: "Bezpłatna wycena"
  };

  const bookingDate = new Date(bookingData.date);
  const timeSlots = {
    "8:00": "8:00 - 10:00",
    "10:00": "10:00 - 12:00", 
    "12:00": "12:00 - 14:00",
    "14:00": "14:00 - 16:00",
    "16:00": "16:00 - 18:00"
  };

  const emailContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
        <h1 style="margin: 0; font-size: 28px;">${fulmarkData.company.name}</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px;">Potwierdzenie rezerwacji</p>
      </div>

      <div style="padding: 30px; background: white;">
        <h2 style="color: #333; margin-bottom: 20px;">Wizyta zarezerwowana!</h2>
        
        <p>Szanowny/a <strong>${bookingData.name}</strong>,</p>
        
        <p>Dziękujemy za rezerwację wizyty online. Oto szczegóły Państwa rezerwacji:</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
          <h3 style="color: #333; margin-top: 0;">Szczegóły wizyty</h3>
          <table style="width: 100%; color: #666;">
            <tr><td style="padding: 5px 0;"><strong>Usługa:</strong></td><td>${serviceLabels[bookingData.service as keyof typeof serviceLabels]}</td></tr>
            <tr><td style="padding: 5px 0;"><strong>Data:</strong></td><td>${formatDate(bookingDate)}</td></tr>
            <tr><td style="padding: 5px 0;"><strong>Godzina:</strong></td><td>${timeSlots[bookingData.timeSlot as keyof typeof timeSlots]}</td></tr>
            <tr><td style="padding: 5px 0;"><strong>Adres:</strong></td><td>${bookingData.address}</td></tr>
            <tr><td style="padding: 5px 0;"><strong>Nr rezerwacji:</strong></td><td>#${serviceOrderId}</td></tr>
          </table>
        </div>

        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
          <h3 style="color: #856404; margin-top: 0;">⚠️ Ważne informacje</h3>
          <ul style="color: #856404; margin: 0;">
            <li><strong>Potwierdzimy termin telefonicznie w ciągu godziny</strong></li>
            <li>W przypadku braku dostępności zaproponujemy alternatywny termin</li>
            <li>Prosimy być dostępnym pod podanym numerem telefonu</li>
            <li>Przygotuj dostęp do urządzenia/miejsca montażu</li>
          </ul>
        </div>

        <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
          <h3 style="color: #155724; margin-top: 0;">Co zabrać ze sobą?</h3>
          <ul style="color: #155724; margin: 0;">
            <li>Dokumenty gwarancyjne urządzenia (jeśli dotyczy)</li>
            <li>Historię poprzednich serwisów</li>
            <li>Pytania dotyczące obsługi urządzenia</li>
          </ul>
        </div>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Kontakt</h3>
          <p style="margin: 0; color: #666;">
            <strong>Telefon:</strong> ${contactInfo.phone}<br>
            <strong>Email:</strong> ${contactInfo.email}<br>
            <strong>Godziny pracy:</strong> Pon-Pt 8:00-18:00, Sob 9:00-15:00
          </p>
        </div>
      </div>

      <div style="background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 12px;">
        <p style="margin: 0;">
          ${fulmarkData.company.fullName}<br>
          ${fulmarkData.company.experience} | Warszawa i okolice<br>
          Tel: ${contactInfo.phone} | Email: ${contactInfo.email}
        </p>
      </div>
    </div>
  `;

  await sendEmail({
    to: bookingData.email,
    subject: `Potwierdzenie rezerwacji - ${serviceLabels[bookingData.service as keyof typeof serviceLabels]} | ${fulmarkData.company.name}`,
    html: emailContent,
    replyTo: contactInfo.email
  });
}

async function sendBookingNotificationToTeam(bookingData: any, customerId: string, serviceOrderId: string) {
  const serviceLabels = {
    montaz: "Montaż klimatyzacji",
    serwis: "Serwis klimatyzacji",
    naprawa: "Naprawa klimatyzacji", 
    wycena: "Bezpłatna wycena"
  };

  const bookingDate = new Date(bookingData.date);
  const timeSlots = {
    "8:00": "8:00 - 10:00",
    "10:00": "10:00 - 12:00",
    "12:00": "12:00 - 14:00", 
    "14:00": "14:00 - 16:00",
    "16:00": "16:00 - 18:00"
  };

  const emailContent = `
    <h2>📅 Nowa rezerwacja online!</h2>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Szczegóły rezerwacji:</h3>
      <p><strong>Usługa:</strong> ${serviceLabels[bookingData.service as keyof typeof serviceLabels]}</p>
      <p><strong>Data:</strong> ${formatDate(bookingDate)}</p>
      <p><strong>Godzina:</strong> ${timeSlots[bookingData.timeSlot as keyof typeof timeSlots]}</p>
      <p><strong>Priorytet:</strong> ${getBookingPriority(bookingData.service).toUpperCase()}</p>
      <p><strong>Nr zlecenia:</strong> #${serviceOrderId}</p>
    </div>

    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Dane klienta:</h3>
      <p><strong>Imię:</strong> ${bookingData.name}</p>
      <p><strong>Telefon:</strong> ${bookingData.phone}</p>
      <p><strong>Email:</strong> ${bookingData.email || "Nie podano"}</p>
      <p><strong>Adres:</strong> ${bookingData.address}</p>
      <p><strong>ID klienta:</strong> ${customerId}</p>
    </div>

    ${bookingData.notes ? `
    <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Uwagi klienta:</h3>
      <p style="font-style: italic;">"${bookingData.notes}"</p>
    </div>
    ` : ""}

    <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Następne kroki:</h3>
      <ul>
        <li><strong>PILNE:</strong> Zadzwoń do klienta w ciągu godziny</li>
        <li>Potwierdź dostępność terminu w kalendarzu</li>
        <li>Sprawdź dojazd i dostępność techniku</li>
        <li>Zaktualizuj status zlecenia w CRM</li>
        <li>Wyślij SMS z przypomnieniem dzień przed wizytą</li>
      </ul>
    </div>

    <p style="color: #666; font-size: 12px;">
      Rezerwacja utworzona automatycznie przez system HVAC-Remix<br>
      Data: ${new Date().toLocaleString("pl-PL")}
    </p>
  `;

  await sendEmail({
    to: contactInfo.email,
    subject: `📅 Nowa rezerwacja: ${bookingData.name} - ${serviceLabels[bookingData.service as keyof typeof serviceLabels]} | ${formatDate(bookingDate)}`,
    html: emailContent,
    priority: getBookingPriority(bookingData.service) === "urgent" ? "high" : "normal"
  });
}

async function sendUrgentBookingSMS(bookingData: any) {
  const bookingDate = new Date(bookingData.date);
  const message = `🚨 PILNA REZERWACJA - NAPRAWA
Klient: ${bookingData.name}
Tel: ${bookingData.phone}
Data: ${formatDate(bookingDate)} ${bookingData.timeSlot}
Adres: ${bookingData.address}
POTWIERDŹ NATYCHMIAST!`;

  await sendSMS({
    to: contactInfo.phone,
    message: message
  });
}
