import type { MetaFunction } from "@remix-run/node";
import { 
  Calendar, 
  Clock, 
  CheckCircle,
  Star,
  Shield,
  Phone,
  Wrench,
  Settings,
  AlertTriangle
} from "lucide-react";
import { BookingForm } from "~/components/website/BookingForm";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { fulmarkData, contactInfo } from "~/data/fulmark-data";

export const meta: MetaFunction = () => [
  { title: "Rezerwacja wizyty online - Fulmark Klimatyzacja | Warszawa" },
  { 
    name: "description", 
    content: "Umów wizytę online z Fulmark. Montaż, serwis i naprawa klimatyzacji w Warszawie. Wybierz termin i zostaw dane - potwierdzimy telefonicznie." 
  },
  { name: "keywords", content: "rezerwacja wizyty, umów wizytę, montaż klimatyzacji warszawa, serwis klimaty<PERSON>, fulmark" },
  { property: "og:title", content: "Umów wizytę online - Fulmark Klimatyzacja" },
  { property: "og:description", content: "Szybka rezerwacja wizyty online. Wybierz termin i usługę." },
  { property: "og:type", content: "website" },
];

export default function RezerwacjaPage() {
  const benefits = [
    {
      icon: Calendar,
      title: "Wygodna rezerwacja",
      description: "Umów wizytę online 24/7 bez konieczności dzwonienia",
      color: "text-blue-400"
    },
    {
      icon: Clock,
      title: "Szybkie potwierdzenie",
      description: "Potwierdzimy termin telefonicznie w ciągu godziny",
      color: "text-green-400"
    },
    {
      icon: CheckCircle,
      title: "Gwarancja terminu",
      description: "Dotrzymujemy umówionych terminów lub informujemy z wyprzedzeniem",
      color: "text-purple-400"
    },
    {
      icon: Star,
      title: "Profesjonalna obsługa",
      description: "Doświadczeni technicy z uprawnieniami i certyfikatami",
      color: "text-yellow-400"
    }
  ];

  const serviceTypes = [
    {
      icon: Wrench,
      title: "Montaż klimatyzacji",
      duration: "2-4 godziny",
      description: "Profesjonalny montaż klimatyzacji LG lub Daikin z gwarancją",
      features: [
        "Bezpłatny pomiar i wycena",
        "Montaż w jednym dniu",
        "Gwarancja na montaż",
        "Instruktaż obsługi"
      ],
      price: "od 800 zł",
      urgent: false
    },
    {
      icon: Settings,
      title: "Serwis klimatyzacji",
      duration: "1-2 godziny", 
      description: "Kompleksowy przegląd i konserwacja urządzenia",
      features: [
        "Czyszczenie filtrów i wymienników",
        "Sprawdzenie ciśnienia czynnika",
        "Kontrola połączeń elektrycznych",
        "Dezynfekcja systemu"
      ],
      price: "od 200 zł",
      urgent: false
    },
    {
      icon: AlertTriangle,
      title: "Naprawa klimatyzacji",
      duration: "1-3 godziny",
      description: "Szybka diagnostyka i naprawa awarii",
      features: [
        "Dojazd w 24 godziny",
        "Profesjonalna diagnostyka",
        "Oryginalne części zamienne",
        "Gwarancja na naprawę"
      ],
      price: "od 150 zł",
      urgent: true
    },
    {
      icon: Star,
      title: "Bezpłatna wycena",
      duration: "30-60 minut",
      description: "Pomiar, doradztwo techniczne i szczegółowa wycena",
      features: [
        "Bezpłatny dojazd",
        "Pomiar pomieszczeń",
        "Doradztwo w wyborze urządzenia",
        "Szczegółowa wycena"
      ],
      price: "GRATIS",
      urgent: false
    }
  ];

  const processSteps = [
    {
      step: 1,
      title: "Wybierz usługę",
      description: "Określ rodzaj potrzebnej usługi i poziom skomplikowania"
    },
    {
      step: 2,
      title: "Wybierz termin",
      description: "Zarezerwuj dogodny dla Ciebie dzień i godzinę wizyty"
    },
    {
      step: 3,
      title: "Podaj dane",
      description: "Zostaw swoje dane kontaktowe i adres wizyty"
    },
    {
      step: 4,
      title: "Potwierdzenie",
      description: "Zadzwonimy w ciągu godziny, aby potwierdzić termin"
    }
  ];

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 animate-pulse"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10 container mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-black bg-gradient-to-r from-blue-400 via-green-400 to-blue-600 bg-clip-text text-transparent mb-6">
            Umów wizytę online
          </h1>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Zarezerwuj termin wizyty w kilku prostych krokach. Wybierz usługę, 
            termin i zostaw swoje dane - potwierdzimy rezerwację telefonicznie.
          </p>
        </div>

        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card key={index} className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:scale-105 transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="inline-flex p-4 rounded-full bg-slate-700/50 mb-4">
                    <Icon className={`h-8 w-8 ${benefit.color}`} />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">{benefit.title}</h3>
                  <p className="text-slate-300 text-sm">{benefit.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Process Steps */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Jak to działa?</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {processSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold text-lg mb-4">
                  {step.step}
                </div>
                <h3 className="text-lg font-bold text-white mb-2">{step.title}</h3>
                <p className="text-slate-300 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Service Types */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-8">Dostępne usługi</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {serviceTypes.map((service, index) => {
              const Icon = service.icon;
              return (
                <Card key={index} className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:scale-105 transition-all duration-300 ${
                  service.urgent ? 'border-red-500/50' : ''
                }`}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xl font-bold text-white flex items-center gap-3">
                        <Icon className={`h-6 w-6 ${service.urgent ? 'text-red-400' : 'text-blue-400'}`} />
                        {service.title}
                        {service.urgent && (
                          <span className="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded-full">
                            PILNE
                          </span>
                        )}
                      </CardTitle>
                      <div className="text-right">
                        <div className={`font-bold ${service.price === 'GRATIS' ? 'text-green-400' : 'text-blue-400'}`}>
                          {service.price}
                        </div>
                        <div className="text-xs text-slate-400">{service.duration}</div>
                      </div>
                    </div>
                    <CardDescription className="text-slate-300">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-400" />
                          <span className="text-slate-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Important Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
          <Card className="bg-blue-500/10 border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-blue-400 flex items-center gap-3">
                <Clock className="h-6 w-6" />
                Godziny pracy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-white">Poniedziałek - Piątek</span>
                <span className="text-blue-400 font-medium">8:00 - 18:00</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white">Sobota</span>
                <span className="text-blue-400 font-medium">9:00 - 15:00</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white">Niedziela</span>
                <span className="text-slate-400">Zamknięte</span>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-green-500/10 border-green-500/30">
            <CardHeader>
              <CardTitle className="text-xl font-bold text-green-400 flex items-center gap-3">
                <Shield className="h-6 w-6" />
                Gwarancje i certyfikaty
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {fulmarkData.certifications.map((cert, index) => (
                <div key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-400" />
                  <span className="text-white text-sm">{cert}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Emergency Contact */}
        <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-6 mb-16">
          <div className="text-center">
            <h3 className="text-xl font-bold text-red-400 mb-2">Awaria klimatyzacji?</h3>
            <p className="text-red-300 mb-4">
              W przypadku pilnej awarii zadzwoń bezpośrednio
            </p>
            <a 
              href={`tel:${contactInfo.phone}`}
              className="inline-flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-all duration-300 hover:scale-105"
            >
              <Phone className="h-5 w-5" />
              {contactInfo.phone}
            </a>
            <p className="text-xs text-red-400 mt-2">
              Serwis awaryjny dostępny 24/7 w sezonie letnim
            </p>
          </div>
        </div>

        {/* Booking Form */}
        <BookingForm />
      </div>
    </main>
  );
}
