// 📈 Forecasting Model - AI-Powered Sales Forecasting & Analytics
// Claude 4 w Augment Framework - DIVINE INTELLIGENCE! 🧠

export interface ForecastEntry {
  id: string;
  name: string;
  description?: string;
  
  // Time Period
  periodStart: Date;
  periodEnd: Date;
  periodType: ForecastPeriod;
  
  // Forecast Values
  predictedRevenue: number;
  lowerBound: number;
  upperBound: number;
  confidence: number; // 0-100%
  
  // Breakdown by Categories
  forecastByService?: ServiceForecast[];
  forecastBySource?: SourceForecast[];
  forecastByOwner?: OwnerForecast[];
  forecastByRegion?: RegionForecast[];
  
  // AI Model Information
  modelUsed: AIModel;
  modelVersion: string;
  trainingDataPeriod: string;
  accuracy?: number; // Historical accuracy %
  
  // Seasonal & Trend Analysis
  seasonalFactors?: SeasonalFactor[];
  trendAnalysis?: TrendAnalysis;
  marketConditions?: MarketCondition[];
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  status: ForecastStatus;
  
  // Validation & Tracking
  actualRevenue?: number; // For completed periods
  variance?: number; // Actual vs Predicted
  accuracyScore?: number; // Model performance
  
  // External Factors
  externalFactors?: ExternalFactor[];
  economicIndicators?: EconomicIndicator[];
  competitorActivity?: CompetitorActivity[];
}

export enum ForecastPeriod {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  CUSTOM = 'custom'
}

export enum AIModel {
  BIELIK_V3 = 'bielik-v3',
  GEMMA3_4B = 'gemma3-4b',
  ENSEMBLE = 'ensemble',
  LINEAR_REGRESSION = 'linear_regression',
  ARIMA = 'arima',
  PROPHET = 'prophet'
}

export enum ForecastStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  ARCHIVED = 'archived'
}

export interface ServiceForecast {
  serviceType: string; // installation, maintenance, repair, etc.
  predictedRevenue: number;
  opportunityCount: number;
  averageDealSize: number;
  confidence: number;
}

export interface SourceForecast {
  source: string; // website, referral, ads, etc.
  predictedRevenue: number;
  leadCount: number;
  conversionRate: number;
  costPerLead?: number;
  roi?: number;
}

export interface OwnerForecast {
  ownerId: string;
  ownerName: string;
  predictedRevenue: number;
  opportunityCount: number;
  winRate: number;
  averageDealSize: number;
}

export interface RegionForecast {
  region: string; // Warszawa, Piaseczno, etc.
  predictedRevenue: number;
  marketPenetration: number;
  growthRate: number;
  competitionLevel: string;
}

export interface SeasonalFactor {
  month: number;
  seasonalIndex: number; // 1.0 = average, >1.0 = above average
  historicalPattern: number[];
  confidence: number;
}

export interface TrendAnalysis {
  overallTrend: 'growing' | 'stable' | 'declining';
  growthRate: number; // % per period
  trendStrength: number; // 0-100%
  changePoints?: ChangePoint[];
  cyclicalPatterns?: CyclicalPattern[];
}

export interface ChangePoint {
  date: Date;
  type: 'growth_acceleration' | 'growth_deceleration' | 'trend_reversal';
  impact: number;
  confidence: number;
}

export interface CyclicalPattern {
  name: string;
  period: number; // days
  amplitude: number;
  phase: number;
}

export interface MarketCondition {
  factor: string;
  impact: 'positive' | 'negative' | 'neutral';
  weight: number; // 0-1
  description: string;
}

export interface ExternalFactor {
  name: string;
  type: 'weather' | 'economic' | 'regulatory' | 'competitive' | 'seasonal';
  impact: number; // -100 to +100
  probability: number; // 0-100%
  description: string;
}

export interface EconomicIndicator {
  name: string;
  value: number;
  trend: 'up' | 'down' | 'stable';
  impact: number;
  source: string;
}

export interface CompetitorActivity {
  competitor: string;
  activity: string;
  impact: 'low' | 'medium' | 'high';
  timeframe: string;
  response?: string;
}

// Historical Sales Data for Training
export interface HistoricalSalesData {
  id: string;
  date: Date;
  revenue: number;
  opportunityCount: number;
  
  // Breakdown
  serviceBreakdown: Record<string, number>;
  sourceBreakdown: Record<string, number>;
  ownerBreakdown: Record<string, number>;
  regionBreakdown: Record<string, number>;
  
  // Context
  seasonalFactors: Record<string, number>;
  externalFactors: Record<string, number>;
  marketConditions: Record<string, number>;
  
  // Metadata
  dataQuality: number; // 0-100%
  completeness: number; // 0-100%
  source: string;
}

// Forecast Accuracy Tracking
export interface ForecastAccuracy {
  id: string;
  forecastId: string;
  
  // Accuracy Metrics
  mape: number; // Mean Absolute Percentage Error
  rmse: number; // Root Mean Square Error
  mae: number; // Mean Absolute Error
  r2: number; // R-squared
  
  // Bias Analysis
  bias: number; // Positive = over-forecasting
  biasDirection: 'over' | 'under' | 'balanced';
  
  // Period Analysis
  periodAccuracy: PeriodAccuracy[];
  
  // Model Performance
  modelPerformance: ModelPerformance;
  
  // Improvement Suggestions
  suggestions: string[];
  
  createdAt: Date;
}

export interface PeriodAccuracy {
  period: string;
  accuracy: number;
  variance: number;
  factors: string[];
}

export interface ModelPerformance {
  trainingAccuracy: number;
  validationAccuracy: number;
  testAccuracy: number;
  overfitting: boolean;
  featureImportance: FeatureImportance[];
}

export interface FeatureImportance {
  feature: string;
  importance: number;
  impact: 'positive' | 'negative';
}

// Forecast Configuration
export interface ForecastConfig {
  id: string;
  name: string;
  
  // Model Settings
  primaryModel: AIModel;
  fallbackModel: AIModel;
  ensembleWeights?: Record<AIModel, number>;
  
  // Data Settings
  trainingPeriod: number; // months
  minimumDataPoints: number;
  dataQualityThreshold: number;
  
  // Forecast Settings
  defaultPeriod: ForecastPeriod;
  confidenceLevel: number; // 95%, 99%, etc.
  updateFrequency: 'daily' | 'weekly' | 'monthly';
  
  // Feature Settings
  enableSeasonality: boolean;
  enableTrends: boolean;
  enableExternalFactors: boolean;
  enableCompetitorAnalysis: boolean;
  
  // Validation Settings
  crossValidationFolds: number;
  holdoutPeriod: number; // months
  accuracyThreshold: number;
  
  // Notification Settings
  alertOnLowAccuracy: boolean;
  alertOnSignificantChange: boolean;
  alertThreshold: number;
  
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

// Forecast Scenario Analysis
export interface ForecastScenario {
  id: string;
  name: string;
  description: string;
  
  // Scenario Parameters
  baselineAdjustment: number; // % change from baseline
  seasonalityAdjustment: Record<number, number>; // month -> adjustment
  externalFactorAdjustments: Record<string, number>;
  
  // Results
  predictedRevenue: number;
  confidenceInterval: [number, number];
  probability: number; // Likelihood of scenario
  
  // Impact Analysis
  impactFactors: ImpactFactor[];
  riskAssessment: RiskAssessment;
  
  createdAt: Date;
  createdBy: string;
}

export interface ImpactFactor {
  factor: string;
  impact: number;
  likelihood: number;
  mitigation?: string;
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high';
  riskFactors: string[];
  mitigationStrategies: string[];
  contingencyPlans: string[];
}

// Forecast Analytics
export interface ForecastAnalytics {
  id: string;
  periodStart: Date;
  periodEnd: Date;
  
  // Performance Metrics
  forecastAccuracy: number;
  revenueVariance: number;
  volumeVariance: number;
  
  // Trend Analysis
  revenueGrowth: number;
  volumeGrowth: number;
  seasonalityStrength: number;
  
  // Predictive Insights
  nextPeriodPrediction: number;
  confidenceLevel: number;
  keyDrivers: string[];
  
  // Recommendations
  actionableInsights: string[];
  optimizationOpportunities: string[];
  riskMitigations: string[];
  
  generatedAt: Date;
  generatedBy: string;
}
