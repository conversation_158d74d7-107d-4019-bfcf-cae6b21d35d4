// 🎯 Opportunity Model - Sales Pipeline Management
// Claude 4 w Augment Framework - PEŁNA MOC! 🚀

export interface Opportunity {
  id: string;
  name: string;
  description?: string;
  
  // Customer & Contact Information
  customerId: string;
  customer?: Customer;
  contactPersonId?: string;
  contactPerson?: Contact;
  
  // Sales Pipeline
  stage: OpportunityStage;
  probability: number; // 0-100%
  value: number; // Expected revenue in PLN
  currency: string;
  
  // Timeline
  expectedCloseDate: Date;
  actualCloseDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Assignment
  ownerId: string; // Sales rep/technician
  owner?: User;
  teamId?: string;
  
  // HVAC-Specific Fields
  serviceType: HVACServiceType;
  equipmentType?: HVACEquipmentType;
  installationComplexity: InstallationComplexity;
  roomCount?: number;
  totalArea?: number; // m²
  buildingType: BuildingType;
  
  // Lead Source & Attribution
  source: LeadSource;
  sourceDetails?: string;
  campaignId?: string;
  referralSource?: string;
  
  // AI-Powered Features
  leadScore: number; // 0-100, AI-calculated
  winProbabilityAI?: number; // AI prediction
  recommendedActions?: string[];
  competitorAnalysis?: CompetitorInfo;
  
  // Communication & Activities
  lastContactDate?: Date;
  nextFollowUpDate?: Date;
  activitiesCount: number;
  emailsCount: number;
  callsCount: number;
  
  // Documents & Quotes
  quoteGenerated: boolean;
  quoteValue?: number;
  quoteSentDate?: Date;
  quoteAcceptedDate?: Date;
  contractSigned: boolean;
  
  // Technical Details
  technicalRequirements?: TechnicalRequirements;
  siteVisitRequired: boolean;
  siteVisitCompleted: boolean;
  siteVisitDate?: Date;
  
  // Financial
  estimatedCost: number;
  actualCost?: number;
  margin?: number;
  paymentTerms?: string;
  
  // Status & Tracking
  status: OpportunityStatus;
  priority: Priority;
  tags: string[];
  notes?: string;
  
  // Automation & Workflows
  automationRules?: AutomationRule[];
  workflowStage?: string;
  lastAutomationRun?: Date;
  
  // Analytics & Reporting
  conversionMetrics?: ConversionMetrics;
  timeInStage?: number; // days
  totalSalesCycle?: number; // days from lead to close
  
  // Integration Fields
  externalId?: string;
  syncedAt?: Date;
  metadata?: Record<string, any>;
}

export enum OpportunityStage {
  LEAD = 'lead',
  QUALIFIED = 'qualified',
  NEEDS_ANALYSIS = 'needs_analysis',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost',
  ON_HOLD = 'on_hold'
}

export enum HVACServiceType {
  INSTALLATION = 'installation',
  MAINTENANCE = 'maintenance',
  REPAIR = 'repair',
  CONSULTATION = 'consultation',
  UPGRADE = 'upgrade',
  EMERGENCY = 'emergency'
}

export enum HVACEquipmentType {
  SPLIT_WALL = 'split_wall',
  SPLIT_CEILING = 'split_ceiling',
  MULTI_SPLIT = 'multi_split',
  DUCTED = 'ducted',
  CASSETTE = 'cassette',
  VRF_VRV = 'vrf_vrv',
  CHILLER = 'chiller',
  HEAT_PUMP = 'heat_pump'
}

export enum InstallationComplexity {
  SIMPLE = 'simple',      // Standard installation, easy access
  MODERATE = 'moderate',  // Some challenges, longer runs
  COMPLEX = 'complex',    // Difficult access, special requirements
  EXTREME = 'extreme'     // Highly complex, custom solutions
}

export enum BuildingType {
  APARTMENT = 'apartment',
  HOUSE = 'house',
  OFFICE = 'office',
  RETAIL = 'retail',
  WAREHOUSE = 'warehouse',
  RESTAURANT = 'restaurant',
  HOTEL = 'hotel',
  HOSPITAL = 'hospital',
  SCHOOL = 'school',
  INDUSTRIAL = 'industrial'
}

export enum LeadSource {
  WEBSITE = 'website',
  PHONE_CALL = 'phone_call',
  EMAIL = 'email',
  REFERRAL = 'referral',
  SOCIAL_MEDIA = 'social_media',
  GOOGLE_ADS = 'google_ads',
  FACEBOOK_ADS = 'facebook_ads',
  TRADE_SHOW = 'trade_show',
  COLD_OUTREACH = 'cold_outreach',
  PARTNER = 'partner',
  EXISTING_CUSTOMER = 'existing_customer'
}

export enum OpportunityStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
  DELETED = 'deleted'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface TechnicalRequirements {
  coolingCapacity?: number; // kW
  heatingCapacity?: number; // kW
  powerRequirements?: string;
  specialFeatures?: string[];
  environmentalFactors?: string[];
  accessibilityNotes?: string;
  existingInfrastructure?: string;
}

export interface CompetitorInfo {
  competitors?: string[];
  competitorPricing?: number;
  competitorAdvantages?: string[];
  ourAdvantages?: string[];
  competitiveStrategy?: string;
}

export interface ConversionMetrics {
  leadToQualified?: number; // days
  qualifiedToProposal?: number; // days
  proposalToClose?: number; // days
  totalCycleTime?: number; // days
  touchpointsCount?: number;
  emailOpenRate?: number;
  emailClickRate?: number;
  callConnectRate?: number;
}

export interface AutomationRule {
  id: string;
  name: string;
  trigger: string;
  action: string;
  isActive: boolean;
  lastExecuted?: Date;
}

// Pipeline Configuration
export interface PipelineConfig {
  stages: PipelineStage[];
  defaultProbabilities: Record<OpportunityStage, number>;
  stageRequirements: Record<OpportunityStage, string[]>;
  automationRules: AutomationRule[];
}

export interface PipelineStage {
  stage: OpportunityStage;
  name: string;
  description: string;
  color: string;
  order: number;
  isActive: boolean;
  requirements?: string[];
  automatedActions?: string[];
}

// AI-Powered Lead Scoring Factors
export interface LeadScoringFactors {
  demographic: number;    // Company size, location, industry
  behavioral: number;     // Website activity, email engagement
  firmographic: number;   // Budget, authority, need, timeline
  technographic: number;  // Current HVAC setup, technology stack
  engagement: number;     // Response rate, meeting attendance
  intent: number;         // Purchase signals, urgency indicators
}

// Sales Forecasting
export interface SalesForecast {
  period: string; // month, quarter, year
  totalValue: number;
  weightedValue: number;
  opportunitiesCount: number;
  averageDealSize: number;
  winRate: number;
  averageSalesCycle: number;
  confidence: number; // AI confidence in forecast
  trends: ForecastTrend[];
}

export interface ForecastTrend {
  metric: string;
  direction: 'up' | 'down' | 'stable';
  percentage: number;
  description: string;
}

// Opportunity Analytics
export interface OpportunityAnalytics {
  conversionRates: Record<OpportunityStage, number>;
  averageTimeInStage: Record<OpportunityStage, number>;
  winLossReasons: WinLossReason[];
  sourcePerformance: SourcePerformance[];
  ownerPerformance: OwnerPerformance[];
  seasonalTrends: SeasonalTrend[];
}

export interface WinLossReason {
  reason: string;
  count: number;
  percentage: number;
  category: 'won' | 'lost';
}

export interface SourcePerformance {
  source: LeadSource;
  leadsCount: number;
  conversionRate: number;
  averageValue: number;
  totalRevenue: number;
  costPerLead?: number;
  roi?: number;
}

export interface OwnerPerformance {
  ownerId: string;
  ownerName: string;
  opportunitiesCount: number;
  winRate: number;
  totalRevenue: number;
  averageDealSize: number;
  averageSalesCycle: number;
}

export interface SeasonalTrend {
  month: number;
  opportunitiesCount: number;
  averageValue: number;
  winRate: number;
  seasonalFactor: number;
}
