// 🧠 Lead Scoring Dashboard - AI-Powered Lead Intelligence
// Claude 4 w Augment Framework - COSMIC INTELLIGENCE! 🌟

import { useState, useEffect } from "react";
import { 
  Brain,
  TrendingUp,
  Target,
  Zap,
  Users,
  Building,
  Activity,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Minus,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import type { Opportunity, LeadScoringFactors } from "~/models/opportunity";

interface LeadScoringDashboardProps {
  opportunities: Opportunity[];
  className?: string;
}

interface ScoreDistribution {
  range: string;
  count: number;
  percentage: number;
  color: string;
}

interface ScoringInsights {
  averageScore: number;
  topPerformingFactors: string[];
  improvementAreas: string[];
  trends: ScoreTrend[];
}

interface ScoreTrend {
  factor: string;
  direction: 'up' | 'down' | 'stable';
  change: number;
  impact: 'high' | 'medium' | 'low';
}

export function LeadScoringDashboard({ opportunities, className = "" }: LeadScoringDashboardProps) {
  const [scoreDistribution, setScoreDistribution] = useState<ScoreDistribution[]>([]);
  const [scoringInsights, setScoringInsights] = useState<ScoringInsights | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>("30d");

  useEffect(() => {
    calculateScoreDistribution();
    generateScoringInsights();
  }, [opportunities, selectedTimeframe]);

  const calculateScoreDistribution = () => {
    const ranges = [
      { range: "90-100", min: 90, max: 100, color: "#22c55e" },
      { range: "80-89", min: 80, max: 89, color: "#84cc16" },
      { range: "70-79", min: 70, max: 79, color: "#eab308" },
      { range: "60-69", min: 60, max: 69, color: "#f97316" },
      { range: "0-59", min: 0, max: 59, color: "#ef4444" }
    ];

    const distribution = ranges.map(range => {
      const count = opportunities.filter(opp => 
        opp.leadScore >= range.min && opp.leadScore <= range.max
      ).length;
      
      return {
        range: range.range,
        count,
        percentage: opportunities.length > 0 ? (count / opportunities.length) * 100 : 0,
        color: range.color
      };
    });

    setScoreDistribution(distribution);
  };

  const generateScoringInsights = () => {
    if (opportunities.length === 0) return;

    const averageScore = opportunities.reduce((sum, opp) => sum + opp.leadScore, 0) / opportunities.length;
    
    // Mock insights - in real implementation, this would come from AI analysis
    const insights: ScoringInsights = {
      averageScore,
      topPerformingFactors: ["Firmographic fit", "Engagement level", "Purchase intent"],
      improvementAreas: ["Behavioral signals", "Technographic match"],
      trends: [
        { factor: "Engagement", direction: "up", change: 12, impact: "high" },
        { factor: "Intent signals", direction: "up", change: 8, impact: "medium" },
        { factor: "Demographic fit", direction: "stable", change: 0, impact: "low" }
      ]
    };

    setScoringInsights(insights);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-400";
    if (score >= 80) return "text-lime-400";
    if (score >= 70) return "text-yellow-400";
    if (score >= 60) return "text-orange-400";
    return "text-red-400";
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default";
    if (score >= 60) return "secondary";
    return "destructive";
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case "up": return <ArrowUp className="h-4 w-4 text-green-400" />;
      case "down": return <ArrowDown className="h-4 w-4 text-red-400" />;
      default: return <Minus className="h-4 w-4 text-slate-400" />;
    }
  };

  const topScoredOpportunities = [...opportunities]
    .sort((a, b) => b.leadScore - a.leadScore)
    .slice(0, 5);

  const lowScoredOpportunities = [...opportunities]
    .sort((a, b) => a.leadScore - b.leadScore)
    .slice(0, 5);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-lg">
            <Brain className="h-6 w-6 text-purple-400" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-white">AI Lead Scoring</h2>
            <p className="text-slate-400">Intelligent lead qualification and prioritization</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="border-purple-500 text-purple-400">
            <Zap className="h-3 w-3 mr-1" />
            AI Powered
          </Badge>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="h-5 w-5 text-blue-400" />
              <div>
                <div className="text-sm text-slate-400">Average Score</div>
                <div className={`text-2xl font-bold ${getScoreColor(scoringInsights?.averageScore || 0)}`}>
                  {scoringInsights?.averageScore.toFixed(1) || 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Star className="h-5 w-5 text-green-400" />
              <div>
                <div className="text-sm text-slate-400">High Quality Leads</div>
                <div className="text-2xl font-bold text-green-400">
                  {scoreDistribution.find(d => d.range === "90-100")?.count || 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-400" />
              <div>
                <div className="text-sm text-slate-400">Needs Attention</div>
                <div className="text-2xl font-bold text-orange-400">
                  {scoreDistribution.find(d => d.range === "0-59")?.count || 0}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-5 w-5 text-purple-400" />
              <div>
                <div className="text-sm text-slate-400">Conversion Rate</div>
                <div className="text-2xl font-bold text-purple-400">
                  {((scoreDistribution.find(d => d.range === "90-100")?.count || 0) / Math.max(opportunities.length, 1) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="distribution" className="space-y-4">
        <TabsList className="bg-slate-800 border-slate-700">
          <TabsTrigger value="distribution" className="data-[state=active]:bg-slate-700">
            Score Distribution
          </TabsTrigger>
          <TabsTrigger value="insights" className="data-[state=active]:bg-slate-700">
            AI Insights
          </TabsTrigger>
          <TabsTrigger value="opportunities" className="data-[state=active]:bg-slate-700">
            Top Opportunities
          </TabsTrigger>
        </TabsList>

        <TabsContent value="distribution" className="space-y-4">
          {/* Score Distribution Chart */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Lead Score Distribution
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {scoreDistribution.map((range) => (
                <div key={range.range} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-300">Score {range.range}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-slate-400">{range.count} leads</span>
                      <span className="text-sm font-medium text-white">
                        {range.percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  <Progress 
                    value={range.percentage} 
                    className="h-2"
                    style={{ 
                      background: `${range.color}20`,
                    }}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {/* AI Insights */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  Top Performing Factors
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {scoringInsights?.topPerformingFactors.map((factor, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full" />
                    <span className="text-slate-300">{factor}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-400" />
                  Improvement Areas
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {scoringInsights?.improvementAreas.map((area, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-400 rounded-full" />
                    <span className="text-slate-300">{area}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Trends */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Scoring Trends
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {scoringInsights?.trends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getTrendIcon(trend.direction)}
                    <span className="text-slate-300">{trend.factor}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={trend.impact === 'high' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {trend.impact} impact
                    </Badge>
                    <span className="text-sm text-slate-400">
                      {trend.change > 0 ? '+' : ''}{trend.change}%
                    </span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="opportunities" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Scored Opportunities */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Star className="h-5 w-5 text-green-400" />
                  Highest Scored Leads
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {topScoredOpportunities.map((opp) => (
                  <div key={opp.id} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <div>
                      <div className="font-medium text-white text-sm">{opp.name}</div>
                      <div className="text-xs text-slate-400">{opp.customer?.name}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getScoreBadgeVariant(opp.leadScore)}>
                        {opp.leadScore}
                      </Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Low Scored Opportunities */}
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-400" />
                  Needs Attention
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {lowScoredOpportunities.map((opp) => (
                  <div key={opp.id} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <div>
                      <div className="font-medium text-white text-sm">{opp.name}</div>
                      <div className="text-xs text-slate-400">{opp.customer?.name}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getScoreBadgeVariant(opp.leadScore)}>
                        {opp.leadScore}
                      </Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
