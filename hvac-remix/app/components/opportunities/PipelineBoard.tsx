// 🎯 Pipeline Board - Visual Sales Pipeline Management
// Claude 4 w Augment Framework - COSMIC-LEVEL UX! ✨

import { useState, useEffect } from "react";
import { useFetcher, useLoaderData } from "@remix-run/react";
import { 
  DragDropContext, 
  Droppable, 
  Draggable,
  DropResult 
} from "@hello-pangea/dnd";
import {
  TrendingUp,
  DollarSign,
  Calendar,
  User,
  Phone,
  Mail,
  MapPin,
  Zap,
  Target,
  Clock,
  AlertCircle,
  CheckCircle,
  Star,
  ArrowRight,
  Plus,
  Filter,
  Search,
  MoreVertical
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Progress } from "~/components/ui/progress";
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from "~/components/ui/tooltip";
import type { Opportunity, OpportunityStage } from "~/models/opportunity";

interface PipelineBoardProps {
  opportunities: Opportunity[];
  stages: PipelineStage[];
  onOpportunityMove: (opportunityId: string, newStage: OpportunityStage) => void;
  onOpportunityClick: (opportunity: Opportunity) => void;
  className?: string;
}

interface PipelineStage {
  stage: OpportunityStage;
  name: string;
  color: string;
  count: number;
  value: number;
}

export function PipelineBoard({ 
  opportunities, 
  stages, 
  onOpportunityMove, 
  onOpportunityClick,
  className = "" 
}: PipelineBoardProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOwner, setSelectedOwner] = useState<string>("all");
  const [filteredOpportunities, setFilteredOpportunities] = useState(opportunities);
  const fetcher = useFetcher();

  useEffect(() => {
    let filtered = opportunities;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(opp => 
        opp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        opp.customer?.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Owner filter
    if (selectedOwner !== "all") {
      filtered = filtered.filter(opp => opp.ownerId === selectedOwner);
    }

    setFilteredOpportunities(filtered);
  }, [opportunities, searchTerm, selectedOwner]);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { draggableId, destination } = result;
    const newStage = destination.droppableId as OpportunityStage;
    
    onOpportunityMove(draggableId, newStage);
  };

  const getOpportunitiesByStage = (stage: OpportunityStage) => {
    return filteredOpportunities.filter(opp => opp.stage === stage);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getServiceTypeIcon = (serviceType: string) => {
    switch (serviceType) {
      case 'installation': return <Zap className="h-4 w-4" />;
      case 'maintenance': return <Target className="h-4 w-4" />;
      case 'repair': return <AlertCircle className="h-4 w-4" />;
      default: return <Star className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header & Filters */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Sales Pipeline</h1>
          <p className="text-slate-400">Manage your HVAC opportunities</p>
        </div>
        
        <div className="flex gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search opportunities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-slate-800 border-slate-700 text-white w-64"
            />
          </div>
          
          <Button variant="outline" className="border-slate-700 text-slate-300">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
            <Plus className="h-4 w-4 mr-2" />
            New Opportunity
          </Button>
        </div>
      </div>

      {/* Pipeline Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {stages.map((stage) => (
          <Card key={stage.stage} className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-white text-sm">{stage.name}</h3>
                <div 
                  className={`w-3 h-3 rounded-full`}
                  style={{ backgroundColor: stage.color }}
                />
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold text-white">{stage.count}</div>
                <div className="text-sm text-slate-400">{formatCurrency(stage.value)}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pipeline Board */}
      <DragDropContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 lg:grid-cols-4 xl:grid-cols-5 gap-6 min-h-[600px]">
          {stages.map((stage) => (
            <div key={stage.stage} className="space-y-4">
              {/* Stage Header */}
              <div className="flex items-center justify-between p-4 bg-slate-800/50 rounded-lg border border-slate-700">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: stage.color }}
                  />
                  <h3 className="font-semibold text-white">{stage.name}</h3>
                </div>
                <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                  {getOpportunitiesByStage(stage.stage).length}
                </Badge>
              </div>

              {/* Droppable Area */}
              <Droppable droppableId={stage.stage}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className={`space-y-3 min-h-[500px] p-2 rounded-lg transition-colors duration-200 ${
                      snapshot.isDraggingOver 
                        ? 'bg-slate-700/30 border-2 border-dashed border-slate-500' 
                        : ''
                    }`}
                  >
                    {getOpportunitiesByStage(stage.stage).map((opportunity, index) => (
                      <Draggable 
                        key={opportunity.id} 
                        draggableId={opportunity.id} 
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <Card
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className={`bg-slate-800/80 border-slate-700 hover:border-slate-600 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg ${
                              snapshot.isDragging ? 'rotate-3 shadow-2xl' : ''
                            }`}
                            onClick={() => onOpportunityClick(opportunity)}
                          >
                            <CardContent className="p-4 space-y-3">
                              {/* Header */}
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium text-white text-sm line-clamp-2">
                                    {opportunity.name}
                                  </h4>
                                  <p className="text-xs text-slate-400 mt-1">
                                    {opportunity.customer?.name}
                                  </p>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div 
                                    className={`w-2 h-2 rounded-full ${getPriorityColor(opportunity.priority)}`}
                                  />
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                    <MoreVertical className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>

                              {/* Value & Probability */}
                              <div className="flex items-center justify-between">
                                <div className="text-lg font-bold text-green-400">
                                  {formatCurrency(opportunity.value)}
                                </div>
                                <div className="flex items-center gap-1">
                                  <TrendingUp className="h-3 w-3 text-blue-400" />
                                  <span className="text-xs text-blue-400">
                                    {opportunity.probability}%
                                  </span>
                                </div>
                              </div>

                              {/* Progress Bar */}
                              <Progress 
                                value={opportunity.probability} 
                                className="h-1"
                                style={{ 
                                  background: `linear-gradient(to right, ${stage.color}20, ${stage.color}40)`
                                }}
                              />

                              {/* Service Type & Lead Score */}
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-1 text-slate-400">
                                  {getServiceTypeIcon(opportunity.serviceType)}
                                  <span className="text-xs capitalize">
                                    {opportunity.serviceType}
                                  </span>
                                </div>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Badge 
                                        variant="outline" 
                                        className={`text-xs ${
                                          opportunity.leadScore >= 80 ? 'border-green-500 text-green-400' :
                                          opportunity.leadScore >= 60 ? 'border-yellow-500 text-yellow-400' :
                                          'border-red-500 text-red-400'
                                        }`}
                                      >
                                        {opportunity.leadScore}
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Lead Score: {opportunity.leadScore}/100</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>

                              {/* Owner & Timeline */}
                              <div className="flex items-center justify-between text-xs text-slate-400">
                                <div className="flex items-center gap-1">
                                  <Avatar className="h-5 w-5">
                                    <AvatarImage src={opportunity.owner?.avatar} />
                                    <AvatarFallback className="text-xs">
                                      {opportunity.owner?.name?.charAt(0) || 'U'}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span>{opportunity.owner?.name || 'Unassigned'}</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  <span>
                                    {opportunity.expectedCloseDate 
                                      ? new Date(opportunity.expectedCloseDate).toLocaleDateString('pl-PL')
                                      : 'No date'
                                    }
                                  </span>
                                </div>
                              </div>

                              {/* AI Insights */}
                              {opportunity.recommendedActions && opportunity.recommendedActions.length > 0 && (
                                <div className="bg-blue-500/10 border border-blue-500/30 rounded p-2">
                                  <div className="flex items-center gap-1 mb-1">
                                    <Zap className="h-3 w-3 text-blue-400" />
                                    <span className="text-xs font-medium text-blue-400">AI Recommendation</span>
                                  </div>
                                  <p className="text-xs text-blue-300 line-clamp-2">
                                    {opportunity.recommendedActions[0]}
                                  </p>
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          ))}
        </div>
      </DragDropContext>
    </div>
  );
}
