import { useEffect } from "react";
import { useLocation } from "@remix-run/react";

interface GoogleAnalyticsProps {
  trackingId: string;
  debug?: boolean;
}

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

export function GoogleAnalytics({ trackingId, debug = false }: GoogleAnalyticsProps) {
  const location = useLocation();

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Initialize Google Analytics
    window.dataLayer = window.dataLayer || [];
    window.gtag = function gtag(...args: any[]) {
      window.dataLayer.push(args);
    };

    window.gtag("js", new Date());
    window.gtag("config", trackingId, {
      page_title: document.title,
      page_location: window.location.href,
      debug_mode: debug,
      // Enhanced ecommerce and conversion tracking
      send_page_view: true,
      anonymize_ip: true, // GDPR compliance
      allow_google_signals: true,
      allow_ad_personalization_signals: true,
    });

    if (debug) {
      console.log("Google Analytics initialized with ID:", trackingId);
    }
  }, [trackingId, debug]);

  // Track page views on route changes
  useEffect(() => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("config", trackingId, {
      page_title: document.title,
      page_location: window.location.href,
    });

    if (debug) {
      console.log("Page view tracked:", location.pathname);
    }
  }, [location, trackingId, debug]);

  return (
    <>
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${trackingId}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${trackingId}', {
              page_title: document.title,
              page_location: window.location.href,
              debug_mode: ${debug},
              send_page_view: true,
              anonymize_ip: true,
              allow_google_signals: true,
              allow_ad_personalization_signals: true,
            });
          `,
        }}
      />
    </>
  );
}

// Analytics event tracking functions
export const analytics = {
  // Track custom events
  trackEvent: (eventName: string, parameters?: Record<string, any>) => {
    if (typeof window === "undefined" || !window.gtag) return;
    
    window.gtag("event", eventName, {
      event_category: "engagement",
      event_label: parameters?.label,
      value: parameters?.value,
      ...parameters,
    });
  },

  // Track conversions (leads, bookings)
  trackConversion: (conversionType: "lead" | "booking" | "call" | "email", value?: number) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "conversion", {
      event_category: "conversion",
      event_label: conversionType,
      value: value || 1,
      currency: "PLN",
    });

    // Track specific conversion events
    window.gtag("event", conversionType, {
      event_category: "conversion",
      value: value || 1,
      currency: "PLN",
    });
  },

  // Track form submissions
  trackFormSubmission: (formType: "contact" | "booking" | "calculator", formData?: Record<string, any>) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "form_submit", {
      event_category: "form",
      event_label: formType,
      form_type: formType,
      ...formData,
    });
  },

  // Track calculator usage
  trackCalculatorUsage: (calculationData: {
    buildingType?: string;
    roomSize?: number;
    roomCount?: string;
    brand?: string;
    complexity?: string;
    totalCost?: number;
  }) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "calculator_use", {
      event_category: "engagement",
      event_label: "cost_calculator",
      building_type: calculationData.buildingType,
      room_size: calculationData.roomSize,
      room_count: calculationData.roomCount,
      brand: calculationData.brand,
      complexity: calculationData.complexity,
      estimated_cost: calculationData.totalCost,
    });
  },

  // Track phone calls
  trackPhoneCall: (source: string = "website") => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "phone_call", {
      event_category: "conversion",
      event_label: "phone_call",
      source: source,
    });
  },

  // Track email clicks
  trackEmailClick: (source: string = "website") => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "email_click", {
      event_category: "conversion", 
      event_label: "email_click",
      source: source,
    });
  },

  // Track service interest
  trackServiceInterest: (serviceType: string, source: string = "website") => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "service_interest", {
      event_category: "engagement",
      event_label: serviceType,
      service_type: serviceType,
      source: source,
    });
  },

  // Track scroll depth
  trackScrollDepth: (percentage: number) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "scroll", {
      event_category: "engagement",
      event_label: `${percentage}%`,
      value: percentage,
    });
  },

  // Track time on page
  trackTimeOnPage: (seconds: number) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "timing_complete", {
      event_category: "engagement",
      event_label: "time_on_page",
      value: seconds,
    });
  },

  // Track external link clicks
  trackExternalLink: (url: string, linkText?: string) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "click", {
      event_category: "outbound",
      event_label: url,
      link_text: linkText,
      link_url: url,
    });
  },

  // Track file downloads
  trackDownload: (fileName: string, fileType: string) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "file_download", {
      event_category: "engagement",
      event_label: fileName,
      file_name: fileName,
      file_type: fileType,
    });
  },

  // Track search queries
  trackSearch: (searchTerm: string, resultsCount?: number) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", "search", {
      event_category: "engagement",
      event_label: searchTerm,
      search_term: searchTerm,
      results_count: resultsCount,
    });
  },

  // Track video interactions
  trackVideo: (action: "play" | "pause" | "complete", videoTitle: string, progress?: number) => {
    if (typeof window === "undefined" || !window.gtag) return;

    window.gtag("event", `video_${action}`, {
      event_category: "video",
      event_label: videoTitle,
      video_title: videoTitle,
      video_progress: progress,
    });
  }
};

// Hook for tracking page views and user behavior
export function useAnalytics() {
  const location = useLocation();

  useEffect(() => {
    // Track scroll depth
    let maxScroll = 0;
    const trackScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
        maxScroll = scrollPercent;
        analytics.trackScrollDepth(scrollPercent);
      }
    };

    // Track time on page
    const startTime = Date.now();
    const trackTimeOnPage = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      if (timeSpent > 30) { // Only track if user spent more than 30 seconds
        analytics.trackTimeOnPage(timeSpent);
      }
    };

    window.addEventListener("scroll", trackScroll);
    window.addEventListener("beforeunload", trackTimeOnPage);

    return () => {
      window.removeEventListener("scroll", trackScroll);
      window.removeEventListener("beforeunload", trackTimeOnPage);
    };
  }, [location]);

  return analytics;
}
