import { useState } from "react";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  Wrench,
  Settings,
  AlertTriangle,
  CheckCircle,
  Star,
  ArrowRight
} from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { fulmarkData } from "~/data/fulmark-data";

interface BookingFormProps {
  className?: string;
  preselectedService?: string;
}

export function BookingForm({ className = "", preselectedService }: BookingFormProps) {
  const actionData = useActionData<any>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  
  const [selectedService, setSelectedService] = useState(preselectedService || "");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("");
  const [selectedDate, setSelectedDate] = useState("");

  const services = [
    { 
      value: "montaz", 
      label: "Montaż klimatyzacji", 
      icon: Wrench,
      duration: "2-4 godziny",
      description: "Profesjonalny montaż z gwarancją",
      price: "od 800 zł"
    },
    { 
      value: "serwis", 
      label: "Serwis klimatyzacji", 
      icon: Settings,
      duration: "1-2 godziny",
      description: "Przegląd, czyszczenie, uzupełnienie czynnika",
      price: "od 200 zł"
    },
    { 
      value: "naprawa", 
      label: "Naprawa klimatyzacji", 
      icon: AlertTriangle,
      duration: "1-3 godziny",
      description: "Diagnostyka i naprawa awarii",
      price: "od 150 zł"
    },
    { 
      value: "wycena", 
      label: "Bezpłatna wycena", 
      icon: Star,
      duration: "30-60 minut",
      description: "Pomiar, doradztwo, wycena",
      price: "GRATIS"
    }
  ];

  const timeSlots = [
    { value: "8:00", label: "8:00 - 10:00" },
    { value: "10:00", label: "10:00 - 12:00" },
    { value: "12:00", label: "12:00 - 14:00" },
    { value: "14:00", label: "14:00 - 16:00" },
    { value: "16:00", label: "16:00 - 18:00" }
  ];

  // Generate next 14 days (excluding Sundays)
  const availableDates = Array.from({ length: 14 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() + i + 1);
    return date;
  }).filter(date => date.getDay() !== 0); // Exclude Sundays

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("pl-PL", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };

  const formatDateValue = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  return (
    <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 text-white ${className}`}>
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-3">
          <Calendar className="h-6 w-6 text-blue-400" />
          Umów wizytę online
        </CardTitle>
        <CardDescription className="text-slate-300 text-lg">
          Wybierz usługę, termin i zostaw swoje dane. Potwierdzimy wizytę telefonicznie.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="flex items-center gap-3 p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div>
              <div className="font-medium text-green-400">Wizyta zarezerwowana!</div>
              <div className="text-sm text-green-300">Potwierdzimy termin telefonicznie w ciągu godziny.</div>
            </div>
          </div>
        )}

        {actionData?.error && (
          <div className="flex items-center gap-3 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div>
              <div className="font-medium text-red-400">Błąd rezerwacji</div>
              <div className="text-sm text-red-300">{actionData.error}</div>
            </div>
          </div>
        )}

        <Form method="post" action="/api/bookings/create" className="space-y-6">
          {/* Service Selection */}
          <div className="space-y-4">
            <Label className="text-white text-lg font-medium">1. Wybierz usługę</Label>
            <RadioGroup value={selectedService} onValueChange={setSelectedService} name="service">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {services.map((service) => {
                  const Icon = service.icon;
                  return (
                    <div key={service.value} className="relative">
                      <RadioGroupItem
                        value={service.value}
                        id={service.value}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={service.value}
                        className={`flex flex-col p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                          selectedService === service.value
                            ? 'border-blue-400 bg-blue-500/20'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        <div className="flex items-center gap-3 mb-2">
                          <Icon className={`h-5 w-5 ${selectedService === service.value ? 'text-blue-400' : 'text-slate-400'}`} />
                          <span className="font-medium">{service.label}</span>
                          <span className={`ml-auto text-sm font-bold ${service.value === 'wycena' ? 'text-green-400' : 'text-blue-400'}`}>
                            {service.price}
                          </span>
                        </div>
                        <p className="text-sm text-slate-300 mb-1">{service.description}</p>
                        <div className="flex items-center gap-2 text-xs text-slate-400">
                          <Clock className="h-3 w-3" />
                          <span>{service.duration}</span>
                        </div>
                      </Label>
                    </div>
                  );
                })}
              </div>
            </RadioGroup>
          </div>

          {/* Date Selection */}
          {selectedService && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">2. Wybierz datę</Label>
              <Select value={selectedDate} onValueChange={setSelectedDate} name="date">
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue placeholder="Wybierz datę wizyty" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700 max-h-60">
                  {availableDates.map((date) => (
                    <SelectItem key={formatDateValue(date)} value={formatDateValue(date)} className="text-white">
                      {formatDate(date)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Time Selection */}
          {selectedDate && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">3. Wybierz godzinę</Label>
              <RadioGroup value={selectedTimeSlot} onValueChange={setSelectedTimeSlot} name="timeSlot">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {timeSlots.map((slot) => (
                    <div key={slot.value} className="relative">
                      <RadioGroupItem
                        value={slot.value}
                        id={slot.value}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={slot.value}
                        className={`flex items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                          selectedTimeSlot === slot.value
                            ? 'border-blue-400 bg-blue-500/20'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        {slot.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>
          )}

          {/* Contact Information */}
          {selectedTimeSlot && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">4. Dane kontaktowe</Label>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-white">Imię i nazwisko *</Label>
                  <Input
                    id="name"
                    name="name"
                    required
                    placeholder="Jan Kowalski"
                    className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-white">Telefon *</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    required
                    placeholder="+48 600 100 901"
                    className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-white">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="address" className="text-white">Adres wizyty *</Label>
                  <Input
                    id="address"
                    name="address"
                    required
                    placeholder="ul. Przykładowa 123, Warszawa"
                    className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes" className="text-white">Dodatkowe informacje</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  placeholder="Dodatkowe informacje o wizycie, dostępie do budynku, specjalne wymagania..."
                  rows={3}
                  className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
                />
              </div>

              {/* Submit Button */}
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-4 text-lg transition-all duration-300 hover:scale-105"
              >
                {isSubmitting ? (
                  "Rezerwowanie..."
                ) : (
                  <>
                    <Calendar className="h-5 w-5 mr-2" />
                    Zarezerwuj wizytę
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </>
                )}
              </Button>
              
              <div className="bg-slate-700/30 p-4 rounded-lg">
                <h4 className="font-medium text-white mb-2">Podsumowanie rezerwacji:</h4>
                <div className="space-y-1 text-sm text-slate-300">
                  <p><strong>Usługa:</strong> {services.find(s => s.value === selectedService)?.label}</p>
                  <p><strong>Data:</strong> {selectedDate && formatDate(new Date(selectedDate))}</p>
                  <p><strong>Godzina:</strong> {timeSlots.find(t => t.value === selectedTimeSlot)?.label}</p>
                  <p><strong>Czas trwania:</strong> {services.find(s => s.value === selectedService)?.duration}</p>
                </div>
              </div>
              
              <p className="text-xs text-slate-400 text-center">
                Rezerwacja zostanie potwierdzona telefonicznie w ciągu godziny. 
                W przypadku braku dostępności zaproponujemy alternatywny termin.
              </p>
            </div>
          )}
        </Form>
      </CardContent>
    </Card>
  );
}
