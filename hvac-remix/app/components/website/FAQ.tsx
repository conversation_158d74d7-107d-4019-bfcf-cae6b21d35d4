import { useState } from "react";
import { 
  ChevronDown, 
  ChevronUp, 
  HelpCircle,
  DollarSign,
  Clock,
  Wrench,
  Settings,
  Shield,
  Thermometer,
  Zap,
  Phone
} from "lucide-react";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { contactInfo } from "~/data/fulmark-data";

interface FAQItem {
  id: string;
  category: "general" | "pricing" | "installation" | "maintenance" | "warranty" | "technical";
  question: string;
  answer: string;
  icon?: any;
  popular?: boolean;
}

interface FAQProps {
  className?: string;
  variant?: "accordion" | "grid" | "compact";
  showCategories?: boolean;
  maxItems?: number;
}

const faqData: FAQItem[] = [
  // General Questions
  {
    id: "1",
    category: "general",
    question: "Ile kosztuje montaż klimatyzacji?",
    answer: "Koszt montażu klimatyzacji zależy od wielu czynników: typu urządzenia, powierzchni pomieszczenia, skomplikowania montażu i wybranej marki. Orientacyjnie: klimatyzator ścienny LG od 2.500 zł, Daikin od 3.000 zł (z montażem). Oferujemy bezpłatną wycenę po wizji lokalnej.",
    icon: DollarSign,
    popular: true
  },
  {
    id: "2", 
    category: "general",
    question: "Jak długo trwa montaż klimatyzacji?",
    answer: "Standardowy montaż klimatyzatora ściennego trwa 3-5 godzin. System multi split (kilka jednostek wewnętrznych) może zająć 1-2 dni robocze. Czas zależy od dostępności miejsca montażu, długości tras instalacyjnych i skomplikowania prac.",
    icon: Clock,
    popular: true
  },
  {
    id: "3",
    category: "general", 
    question: "Jakie marki klimatyzacji obsługujecie?",
    answer: "Specjalizujemy się w markach LG i Daikin - jesteśmy autoryzowanym serwisem obu marek. To gwarancja oryginalnych części, profesjonalnego serwisu i zachowania gwarancji producenta. Obie marki oferują wysoką jakość i niezawodność.",
    icon: Shield,
    popular: true
  },
  {
    id: "4",
    category: "installation",
    question: "Czy potrzebne są pozwolenia na montaż klimatyzacji?",
    answer: "W większości przypadków montaż klimatyzacji w mieszkaniu lub domu jednorodzinnym nie wymaga pozwoleń. Wyjątek stanowią budynki zabytkowe lub gdy jednostka zewnętrzna ma być montowana na elewacji frontowej w centrum miasta. Zawsze sprawdzamy lokalne przepisy.",
    icon: Wrench
  },
  {
    id: "5",
    category: "installation",
    question: "Gdzie najlepiej zamontować klimatyzację?",
    answer: "Jednostka wewnętrzna powinna być montowana w miejscu zapewniającym równomierny obieg powietrza, z dala od bezpośredniego nasłonecznienia i źródeł ciepła. Jednostka zewnętrzna wymaga dobrej wentylacji i łatwego dostępu serwisowego. Doradzamy optymalne miejsce podczas bezpłatnej wyceny.",
    icon: Thermometer
  },
  {
    id: "6",
    category: "maintenance",
    question: "Jak często należy serwisować klimatyzację?",
    answer: "Zalecamy przegląd serwisowy raz w roku, najlepiej przed sezonem letnim. Obejmuje on czyszczenie filtrów i wymienników, sprawdzenie ciśnienia czynnika, kontrolę połączeń elektrycznych i dezynfekcję systemu. Regularne serwisowanie wydłuża żywotność urządzenia i zapewnia efektywną pracę.",
    icon: Settings,
    popular: true
  },
  {
    id: "7",
    category: "maintenance",
    question: "Czy mogę sam czyścić filtry w klimatyzacji?",
    answer: "Tak, podstawowe filtry w jednostce wewnętrznej można czyścić samodzielnie co 2-4 tygodnie w sezonie. Wystarczy je wyjąć, przepłukać letnią wodą z delikatnym detergentem i wysuszyć. Jednak pełne czyszczenie wymienników i systemu powinno być wykonywane przez serwis.",
    icon: Settings
  },
  {
    id: "8",
    category: "warranty",
    question: "Jaka jest gwarancja na montaż i urządzenie?",
    answer: "Urządzenia LG i Daikin mają 5-7 lat gwarancji producenta. Dodatkowo udzielamy 2 lata gwarancji na wykonany montaż. Gwarancja obejmuje bezpłatne naprawy, wymianę wadliwych części i dojazdy serwisowe. Warunkiem jest coroczny przegląd serwisowy.",
    icon: Shield,
    popular: true
  },
  {
    id: "9",
    category: "technical",
    question: "Jaka moc klimatyzacji jest potrzebna do mojego pomieszczenia?",
    answer: "Orientacyjnie: 1 kW mocy chłodniczej na 10-12 m² przy wysokości 2,7m. Jednak dokładna moc zależy od izolacji, nasłonecznienia, liczby okien i źródeł ciepła. Podczas bezpłatnej wyceny wykonujemy profesjonalne obliczenia doboru mocy.",
    icon: Zap
  },
  {
    id: "10",
    category: "technical",
    question: "Czy klimatyzacja może grzać zimą?",
    answer: "Tak, nowoczesne klimatyzatory to pompy ciepła, które mogą zarówno chłodzić jak i grzać. Są bardzo efektywne energetycznie - na 1 kW energii elektrycznej produkują 3-4 kW ciepła. Sprawdzają się do temperatury zewnętrznej około -15°C.",
    icon: Thermometer
  },
  {
    id: "11",
    category: "pricing",
    question: "Czy oferujecie płatność ratalną?",
    answer: "Tak, współpracujemy z firmami leasingowymi i oferujemy możliwość rozłożenia płatności na raty. Dostępne są różne formy finansowania: leasing, kredyt konsumencki czy raty 0%. Szczegóły ustalamy podczas wyceny.",
    icon: DollarSign
  },
  {
    id: "12",
    category: "general",
    question: "Czy przyjadą Państwo w przypadku awarii?",
    answer: `Tak, oferujemy serwis awaryjny. W sezonie letnim (maj-wrzesień) dostępny 24/7. Standardowo staramy się dojechać w ciągu 24 godzin. W przypadku pilnej awarii zadzwoń: ${contactInfo.phone}`,
    icon: Phone,
    popular: true
  }
];

export function FAQ({ 
  className = "", 
  variant = "accordion", 
  showCategories = true,
  maxItems 
}: FAQProps) {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const categories = {
    all: { label: "Wszystkie", icon: HelpCircle },
    general: { label: "Ogólne", icon: HelpCircle },
    pricing: { label: "Ceny", icon: DollarSign },
    installation: { label: "Montaż", icon: Wrench },
    maintenance: { label: "Serwis", icon: Settings },
    warranty: { label: "Gwarancja", icon: Shield },
    technical: { label: "Techniczne", icon: Zap }
  };

  const filteredFAQ = selectedCategory === "all" 
    ? faqData 
    : faqData.filter(item => item.category === selectedCategory);

  const displayedFAQ = maxItems 
    ? filteredFAQ.slice(0, maxItems)
    : filteredFAQ;

  const popularFAQ = faqData.filter(item => item.popular);

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const FAQItem = ({ item }: { item: FAQItem }) => {
    const isOpen = openItems.includes(item.id);
    const Icon = item.icon || HelpCircle;

    return (
      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 hover:border-slate-600 transition-all duration-300">
        <CardContent className="p-0">
          <button
            onClick={() => toggleItem(item.id)}
            className="w-full p-6 text-left flex items-center justify-between hover:bg-slate-700/30 transition-colors duration-200"
          >
            <div className="flex items-center gap-3 flex-1">
              <Icon className="h-5 w-5 text-blue-400 flex-shrink-0" />
              <span className="font-medium text-white">{item.question}</span>
              {item.popular && (
                <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 text-xs rounded-full">
                  Popularne
                </span>
              )}
            </div>
            {isOpen ? (
              <ChevronUp className="h-5 w-5 text-slate-400" />
            ) : (
              <ChevronDown className="h-5 w-5 text-slate-400" />
            )}
          </button>
          
          {isOpen && (
            <div className="px-6 pb-6">
              <div className="pl-8 text-slate-300 leading-relaxed">
                {item.answer}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (variant === "compact") {
    return (
      <div className={`space-y-4 ${className}`}>
        <h3 className="text-xl font-bold text-white">Najczęściej zadawane pytania</h3>
        <div className="space-y-3">
          {popularFAQ.slice(0, 3).map((item) => (
            <div key={item.id} className="bg-slate-800/30 rounded-lg p-4">
              <h4 className="font-medium text-white mb-2">{item.question}</h4>
              <p className="text-sm text-slate-300 line-clamp-2">{item.answer}</p>
            </div>
          ))}
        </div>
        <Button variant="outline" className="w-full">
          Zobacz wszystkie pytania
        </Button>
      </div>
    );
  }

  if (variant === "grid") {
    return (
      <div className={`space-y-6 ${className}`}>
        {showCategories && (
          <div className="flex flex-wrap gap-2">
            {Object.entries(categories).map(([key, category]) => {
              const Icon = category.icon;
              return (
                <Button
                  key={key}
                  variant={selectedCategory === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(key)}
                  className="text-xs"
                >
                  <Icon className="h-3 w-3 mr-1" />
                  {category.label}
                </Button>
              );
            })}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {displayedFAQ.map((item) => (
            <FAQItem key={item.id} item={item} />
          ))}
        </div>
      </div>
    );
  }

  // Accordion variant (default)
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-2">Najczęściej zadawane pytania</h2>
        <p className="text-slate-300">Znajdź odpowiedzi na najważniejsze pytania o klimatyzację</p>
      </div>

      {showCategories && (
        <div className="flex flex-wrap gap-2 justify-center">
          {Object.entries(categories).map(([key, category]) => {
            const Icon = category.icon;
            return (
              <Button
                key={key}
                variant={selectedCategory === key ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(key)}
                className="text-xs"
              >
                <Icon className="h-3 w-3 mr-1" />
                {category.label}
              </Button>
            );
          })}
        </div>
      )}

      <div className="space-y-4">
        {displayedFAQ.map((item) => (
          <FAQItem key={item.id} item={item} />
        ))}
      </div>

      {filteredFAQ.length === 0 && (
        <div className="text-center py-8">
          <HelpCircle className="h-12 w-12 text-slate-400 mx-auto mb-4" />
          <p className="text-slate-400">Brak pytań w tej kategorii</p>
        </div>
      )}

      <div className="text-center">
        <p className="text-slate-400 mb-4">Nie znalazłeś odpowiedzi na swoje pytanie?</p>
        <Button asChild className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
          <a href={`tel:${contactInfo.phone}`}>
            <Phone className="h-4 w-4 mr-2" />
            Zadzwoń: {contactInfo.phone}
          </a>
        </Button>
      </div>
    </div>
  );
}
