import { useState, useEffect } from "react";
import { 
  Calculator, 
  Home, 
  Building, 
  Thermometer,
  Zap,
  Star,
  ArrowRight,
  Info,
  Phone,
  Mail
} from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Slider } from "~/components/ui/slider";
import { ContactForm } from "./ContactForm";
import { fulmarkData } from "~/data/fulmark-data";

interface CostCalculatorProps {
  className?: string;
}

interface CalculationResult {
  basePrice: number;
  installationCost: number;
  totalCost: number;
  brand: string;
  systemType: string;
  roomSize: number;
  complexity: string;
}

export function CostCalculator({ className = "" }: CostCalculatorProps) {
  const [buildingType, setBuildingType] = useState("");
  const [roomSize, setRoomSize] = useState([25]);
  const [roomCount, setRoomCount] = useState("");
  const [brand, setBrand] = useState("");
  const [complexity, setComplexity] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [calculation, setCalculation] = useState<CalculationResult | null>(null);
  const [showContactForm, setShowContactForm] = useState(false);

  const buildingTypes = [
    { value: "mieszkanie", label: "Mieszkanie", icon: Home, multiplier: 1.0 },
    { value: "dom", label: "Dom jednorodzinny", icon: Building, multiplier: 1.2 },
    { value: "biuro", label: "Biuro/Lokal", icon: Building, multiplier: 1.5 }
  ];

  const roomCounts = [
    { value: "1", label: "1 pomieszczenie", multiplier: 1.0 },
    { value: "2", label: "2 pomieszczenia", multiplier: 1.8 },
    { value: "3", label: "3 pomieszczenia", multiplier: 2.5 },
    { value: "4", label: "4+ pomieszczeń", multiplier: 3.2 }
  ];

  const brands = [
    { value: "lg", label: "LG", basePrice: 2800, premium: false },
    { value: "daikin", label: "Daikin", basePrice: 3200, premium: true }
  ];

  const complexityLevels = [
    { value: "standard", label: "Standardowy montaż", multiplier: 1.0, description: "Krótkie trasy, łatwy dostęp" },
    { value: "medium", label: "Średni poziom trudności", multiplier: 1.3, description: "Dłuższe trasy, dodatkowe prace" },
    { value: "complex", label: "Skomplikowany montaż", multiplier: 1.6, description: "Trudny dostęp, specjalne wymagania" }
  ];

  const calculateCost = () => {
    if (!buildingType || !roomCount || !brand || !complexity) return;

    const selectedBuilding = buildingTypes.find(b => b.value === buildingType);
    const selectedRoomCount = roomCounts.find(r => r.value === roomCount);
    const selectedBrand = brands.find(b => b.value === brand);
    const selectedComplexity = complexityLevels.find(c => c.value === complexity);

    if (!selectedBuilding || !selectedRoomCount || !selectedBrand || !selectedComplexity) return;

    // Base calculation
    const sizeMultiplier = Math.max(1, roomSize[0] / 25); // Base size 25m²
    const basePrice = selectedBrand.basePrice * sizeMultiplier;
    
    // Apply multipliers
    const buildingMultiplier = selectedBuilding.multiplier;
    const roomMultiplier = selectedRoomCount.multiplier;
    const complexityMultiplier = selectedComplexity.multiplier;
    
    const equipmentCost = basePrice * buildingMultiplier * roomMultiplier;
    const installationCost = equipmentCost * 0.4 * complexityMultiplier; // Installation is ~40% of equipment cost
    const totalCost = equipmentCost + installationCost;

    const result: CalculationResult = {
      basePrice: equipmentCost,
      installationCost,
      totalCost,
      brand: selectedBrand.label,
      systemType: roomCount === "1" ? "Split" : "Multi Split",
      roomSize: roomSize[0],
      complexity: selectedComplexity.label
    };

    setCalculation(result);
    setShowResults(true);
  };

  useEffect(() => {
    if (buildingType && roomCount && brand && complexity) {
      calculateCost();
    }
  }, [buildingType, roomSize, roomCount, brand, complexity]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pl-PL', {
      style: 'currency',
      currency: 'PLN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  return (
    <div className={`space-y-8 ${className}`}>
      <Card className="bg-slate-800/50 backdrop-blur-sm border-slate-700 text-white">
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center gap-3">
            <Calculator className="h-6 w-6 text-green-400" />
            Kalkulator kosztów klimatyzacji
          </CardTitle>
          <CardDescription className="text-slate-300 text-lg">
            Sprawdź orientacyjny koszt montażu klimatyzacji w Twoim domu lub biurze
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Building Type */}
          <div className="space-y-4">
            <Label className="text-white text-lg font-medium">1. Typ budynku</Label>
            <RadioGroup value={buildingType} onValueChange={setBuildingType}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {buildingTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <div key={type.value} className="relative">
                      <RadioGroupItem
                        value={type.value}
                        id={type.value}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={type.value}
                        className={`flex flex-col items-center p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                          buildingType === type.value
                            ? 'border-green-400 bg-green-500/20'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        <Icon className={`h-8 w-8 mb-2 ${buildingType === type.value ? 'text-green-400' : 'text-slate-400'}`} />
                        <span className="font-medium text-center">{type.label}</span>
                      </Label>
                    </div>
                  );
                })}
              </div>
            </RadioGroup>
          </div>

          {/* Room Size */}
          {buildingType && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">
                2. Powierzchnia głównego pomieszczenia: {roomSize[0]} m²
              </Label>
              <div className="px-4">
                <Slider
                  value={roomSize}
                  onValueChange={setRoomSize}
                  max={100}
                  min={10}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-slate-400 mt-2">
                  <span>10 m²</span>
                  <span>50 m²</span>
                  <span>100 m²</span>
                </div>
              </div>
            </div>
          )}

          {/* Room Count */}
          {roomSize[0] && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">3. Liczba pomieszczeń do klimatyzacji</Label>
              <Select value={roomCount} onValueChange={setRoomCount}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                  <SelectValue placeholder="Wybierz liczbę pomieszczeń" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {roomCounts.map((count) => (
                    <SelectItem key={count.value} value={count.value} className="text-white">
                      {count.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Brand Selection */}
          {roomCount && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">4. Preferowana marka</Label>
              <RadioGroup value={brand} onValueChange={setBrand}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {brands.map((brandOption) => (
                    <div key={brandOption.value} className="relative">
                      <RadioGroupItem
                        value={brandOption.value}
                        id={brandOption.value}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={brandOption.value}
                        className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                          brand === brandOption.value
                            ? 'border-blue-400 bg-blue-500/20'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <span className="font-medium text-lg">{brandOption.label}</span>
                          {brandOption.premium && (
                            <Star className="h-4 w-4 text-yellow-400" />
                          )}
                        </div>
                        <span className="text-sm text-slate-400">
                          od {formatPrice(brandOption.basePrice)}
                        </span>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>
          )}

          {/* Complexity */}
          {brand && (
            <div className="space-y-4">
              <Label className="text-white text-lg font-medium">5. Poziom skomplikowania montażu</Label>
              <RadioGroup value={complexity} onValueChange={setComplexity}>
                <div className="space-y-3">
                  {complexityLevels.map((level) => (
                    <div key={level.value} className="relative">
                      <RadioGroupItem
                        value={level.value}
                        id={level.value}
                        className="peer sr-only"
                      />
                      <Label
                        htmlFor={level.value}
                        className={`flex items-center justify-between p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 hover:scale-105 ${
                          complexity === level.value
                            ? 'border-purple-400 bg-purple-500/20'
                            : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                        }`}
                      >
                        <div>
                          <div className="font-medium">{level.label}</div>
                          <div className="text-sm text-slate-400">{level.description}</div>
                        </div>
                        <div className="text-sm text-slate-400">
                          +{Math.round((level.multiplier - 1) * 100)}%
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>
          )}

          {/* Results */}
          {showResults && calculation && (
            <div className="space-y-6 pt-6 border-t border-slate-600">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-white mb-2">Orientacyjny koszt</h3>
                <div className="text-4xl font-bold text-green-400 mb-4">
                  {formatPrice(calculation.totalCost)}
                </div>
                <p className="text-slate-300">
                  Koszt może się różnić w zależności od specyfiki obiektu
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="bg-slate-700/30 border-slate-600">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Thermometer className="h-5 w-5 text-blue-400" />
                      <span className="font-medium text-white">Urządzenie</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-400">
                      {formatPrice(calculation.basePrice)}
                    </div>
                    <div className="text-sm text-slate-400">
                      {calculation.brand} {calculation.systemType}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-700/30 border-slate-600">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-5 w-5 text-orange-400" />
                      <span className="font-medium text-white">Montaż</span>
                    </div>
                    <div className="text-2xl font-bold text-orange-400">
                      {formatPrice(calculation.installationCost)}
                    </div>
                    <div className="text-sm text-slate-400">
                      {calculation.complexity}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Info className="h-5 w-5 text-blue-400 mt-0.5" />
                  <div className="text-sm text-blue-300">
                    <p className="font-medium mb-1">Wycena orientacyjna</p>
                    <p>
                      Dokładny koszt zostanie ustalony po bezpłatnej wizji lokalnej. 
                      Cena może się różnić w zależności od dostępności, specyfiki obiektu i wybranych opcji.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  onClick={() => setShowContactForm(true)}
                  className="flex-1 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold py-3 transition-all duration-300 hover:scale-105"
                >
                  <Mail className="h-5 w-5 mr-2" />
                  Zamów bezpłatną wycenę
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
                
                <Button 
                  variant="outline"
                  className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
                  onClick={() => window.open(`tel:${fulmarkData.company.phone}`)}
                >
                  <Phone className="h-5 w-5 mr-2" />
                  Zadzwoń teraz
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contact Form */}
      {showContactForm && (
        <ContactForm 
          variant="default"
          source={`calculator-${calculation?.totalCost ? Math.round(calculation.totalCost) : 'unknown'}`}
        />
      )}
    </div>
  );
}
