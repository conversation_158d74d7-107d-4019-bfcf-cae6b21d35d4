import { useState, useEffect } from "react";
import { 
  Star, 
  Quote, 
  ChevronLeft, 
  ChevronRight,
  MapPin,
  Calendar,
  <PERSON>ch,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle
} from "lucide-react";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";

interface Testimonial {
  id: string;
  name: string;
  location: string;
  service: "montaz" | "serwis" | "naprawa" | "wycena";
  rating: number;
  date: string;
  review: string;
  verified: boolean;
  projectDetails?: {
    brand?: string;
    model?: string;
    rooms?: number;
    duration?: string;
  };
}

interface TestimonialsProps {
  className?: string;
  variant?: "carousel" | "grid" | "compact";
  showFilters?: boolean;
  maxItems?: number;
}

const testimonials: Testimonial[] = [
  {
    id: "1",
    name: "<PERSON>",
    location: "Warszawa, Mokotów",
    service: "montaz",
    rating: 5,
    date: "2024-01-15",
    review: "Profesjonalny montaż klimatyzacji LG w salonie. Technicy punktualni, wszystko wykonane zgodnie z umową. Klimatyzacja działa bezawaryjnie już 3 miesiące. Polecam!",
    verified: true,
    projectDetails: {
      brand: "LG",
      model: "Artcool Gallery",
      rooms: 1,
      duration: "4 godziny"
    }
  },
  {
    id: "2", 
    name: "Marek Nowak",
    location: "Piaseczno",
    service: "serwis",
    rating: 5,
    date: "2024-02-03",
    review: "Szybki i profesjonalny serwis klimatyzacji Daikin. Technik dokładnie sprawdził całe urządzenie, wymienił filtry i uzupełnił czynnik. Bardzo dobra komunikacja.",
    verified: true,
    projectDetails: {
      brand: "Daikin",
      model: "Sensira",
      duration: "1.5 godziny"
    }
  },
  {
    id: "3",
    name: "Katarzyna Wiśniewska", 
    location: "Warszawa, Ursynów",
    service: "naprawa",
    rating: 5,
    date: "2024-01-28",
    review: "Awaria klimatyzacji w środku lata - panika! Fulmark przyjechał tego samego dnia, szybko zdiagnozował problem i naprawił. Ceny uczciwe, obsługa na najwyższym poziomie.",
    verified: true,
    projectDetails: {
      brand: "LG",
      duration: "2 godziny"
    }
  },
  {
    id: "4",
    name: "Tomasz Kowalczyk",
    location: "Warszawa, Wilanów", 
    service: "montaz",
    rating: 5,
    date: "2024-02-10",
    review: "Montaż systemu multi split Daikin w całym domu. Profesjonalne doradztwo przy wyborze urządzeń, terminowy montaż, wszystko działa perfekcyjnie. Bardzo polecam!",
    verified: true,
    projectDetails: {
      brand: "Daikin",
      model: "Emura",
      rooms: 4,
      duration: "2 dni"
    }
  },
  {
    id: "5",
    name: "Agnieszka Zielińska",
    location: "Warszawa, Śródmieście",
    service: "wycena",
    rating: 5,
    date: "2024-02-20",
    review: "Bezpłatna wycena wykonana bardzo dokładnie. Technik wszystko wytłumaczył, pokazał różne opcje, pomógł wybrać najlepsze rozwiązanie. Profesjonalne podejście.",
    verified: true,
    projectDetails: {
      duration: "45 minut"
    }
  },
  {
    id: "6",
    name: "Piotr Jankowski",
    location: "Konstancin-Jeziorna",
    service: "montaz",
    rating: 5,
    date: "2024-01-05",
    review: "Montaż klimatyzacji LG w biurze. Wszystko wykonane zgodnie z harmonogramem, czysto i profesjonalnie. Instruktaż obsługi bardzo pomocny. Polecam każdemu!",
    verified: true,
    projectDetails: {
      brand: "LG",
      model: "Standard Plus",
      rooms: 2,
      duration: "6 godzin"
    }
  }
];

export function Testimonials({ 
  className = "", 
  variant = "carousel", 
  showFilters = false,
  maxItems 
}: TestimonialsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [filter, setFilter] = useState<string>("all");
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const serviceLabels = {
    montaz: "Montaż",
    serwis: "Serwis", 
    naprawa: "Naprawa",
    wycena: "Wycena"
  };

  const serviceIcons = {
    montaz: Wrench,
    serwis: Settings,
    naprawa: AlertTriangle,
    wycena: Star
  };

  const filteredTestimonials = filter === "all" 
    ? testimonials 
    : testimonials.filter(t => t.service === filter);

  const displayedTestimonials = maxItems 
    ? filteredTestimonials.slice(0, maxItems)
    : filteredTestimonials;

  // Auto-play carousel
  useEffect(() => {
    if (variant === "carousel" && isAutoPlaying && displayedTestimonials.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [variant, isAutoPlaying, displayedTestimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % displayedTestimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + displayedTestimonials.length) % displayedTestimonials.length);
    setIsAutoPlaying(false);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-slate-400"
        }`}
      />
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pl-PL", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  };

  const TestimonialCard = ({ testimonial, isActive = true }: { testimonial: Testimonial; isActive?: boolean }) => {
    const ServiceIcon = serviceIcons[testimonial.service];
    
    return (
      <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 transition-all duration-300 ${
        isActive ? "opacity-100 scale-100" : "opacity-70 scale-95"
      }`}>
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                {testimonial.name.charAt(0)}
              </div>
              <div>
                <h4 className="font-semibold text-white">{testimonial.name}</h4>
                <div className="flex items-center gap-1 text-sm text-slate-400">
                  <MapPin className="h-3 w-3" />
                  {testimonial.location}
                </div>
              </div>
            </div>
            {testimonial.verified && (
              <div className="flex items-center gap-1 text-green-400 text-xs">
                <CheckCircle className="h-3 w-3" />
                Zweryfikowana
              </div>
            )}
          </div>

          {/* Service & Rating */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <ServiceIcon className="h-4 w-4 text-blue-400" />
              <span className="text-sm text-slate-300">{serviceLabels[testimonial.service]}</span>
            </div>
            <div className="flex items-center gap-1">
              {renderStars(testimonial.rating)}
            </div>
          </div>

          {/* Review */}
          <div className="relative mb-4">
            <Quote className="absolute -top-2 -left-2 h-6 w-6 text-blue-400/30" />
            <p className="text-slate-300 leading-relaxed pl-4">
              "{testimonial.review}"
            </p>
          </div>

          {/* Project Details */}
          {testimonial.projectDetails && (
            <div className="bg-slate-700/30 rounded-lg p-3 mb-4">
              <h5 className="text-xs font-medium text-slate-400 mb-2">Szczegóły projektu:</h5>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {testimonial.projectDetails.brand && (
                  <div>
                    <span className="text-slate-400">Marka:</span>
                    <span className="text-white ml-1">{testimonial.projectDetails.brand}</span>
                  </div>
                )}
                {testimonial.projectDetails.model && (
                  <div>
                    <span className="text-slate-400">Model:</span>
                    <span className="text-white ml-1">{testimonial.projectDetails.model}</span>
                  </div>
                )}
                {testimonial.projectDetails.rooms && (
                  <div>
                    <span className="text-slate-400">Pomieszczenia:</span>
                    <span className="text-white ml-1">{testimonial.projectDetails.rooms}</span>
                  </div>
                )}
                {testimonial.projectDetails.duration && (
                  <div>
                    <span className="text-slate-400">Czas:</span>
                    <span className="text-white ml-1">{testimonial.projectDetails.duration}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Date */}
          <div className="flex items-center gap-1 text-xs text-slate-400">
            <Calendar className="h-3 w-3" />
            {formatDate(testimonial.date)}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (variant === "compact") {
    return (
      <div className={`space-y-4 ${className}`}>
        <h3 className="text-xl font-bold text-white">Opinie klientów</h3>
        <div className="space-y-3">
          {displayedTestimonials.slice(0, 3).map((testimonial) => (
            <div key={testimonial.id} className="bg-slate-800/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="flex">{renderStars(testimonial.rating)}</div>
                <span className="text-sm text-slate-400">- {testimonial.name}</span>
              </div>
              <p className="text-sm text-slate-300 line-clamp-2">"{testimonial.review}"</p>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (variant === "grid") {
    return (
      <div className={`space-y-6 ${className}`}>
        {showFilters && (
          <div className="flex flex-wrap gap-2">
            <Button
              variant={filter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("all")}
              className="text-xs"
            >
              Wszystkie
            </Button>
            {Object.entries(serviceLabels).map(([key, label]) => (
              <Button
                key={key}
                variant={filter === key ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter(key)}
                className="text-xs"
              >
                {label}
              </Button>
            ))}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayedTestimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    );
  }

  // Carousel variant (default)
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-2">Co mówią nasi klienci</h2>
        <p className="text-slate-300">Ponad {testimonials.length} zadowolonych klientów w Warszawie i okolicach</p>
      </div>

      <div className="relative">
        <div className="overflow-hidden">
          <div 
            className="flex transition-transform duration-500 ease-in-out"
            style={{ transform: `translateX(-${currentIndex * 100}%)` }}
          >
            {displayedTestimonials.map((testimonial, index) => (
              <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                <TestimonialCard 
                  testimonial={testimonial} 
                  isActive={index === currentIndex}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Navigation */}
        {displayedTestimonials.length > 1 && (
          <>
            <Button
              variant="outline"
              size="icon"
              onClick={prevTestimonial}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-slate-800/80 border-slate-600 hover:bg-slate-700"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-slate-800/80 border-slate-600 hover:bg-slate-700"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Dots indicator */}
        <div className="flex justify-center gap-2 mt-6">
          {displayedTestimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setCurrentIndex(index);
                setIsAutoPlaying(false);
              }}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex ? "bg-blue-400 w-6" : "bg-slate-600"
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
