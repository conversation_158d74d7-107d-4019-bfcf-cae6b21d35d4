import { useState } from "react";
import { Form, useActionData, useNavigation } from "@remix-run/react";
import { 
  Phone, 
  Mail, 
  MapPin, 
  Send, 
  CheckCircle, 
  AlertCircle,
  User,
  MessageSquare,
  Wrench,
  Calendar,
  Star
} from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { contactInfo, fulmarkData } from "~/data/fulmark-data";

interface ContactFormProps {
  className?: string;
  variant?: "default" | "compact" | "inline";
  source?: string;
}

export function ContactForm({ className = "", variant = "default", source = "website" }: ContactFormProps) {
  const actionData = useActionData<any>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";
  const [selectedService, setSelectedService] = useState("");

  const services = [
    { value: "montaz", label: "Montaż klimatyzacji", icon: Wrench },
    { value: "serwis", label: "Serwis klimatyzacji", icon: Calendar },
    { value: "naprawa", label: "Naprawa klimatyzacji", icon: AlertCircle },
    { value: "wycena", label: "Bezpłatna wycena", icon: Star },
    { value: "inne", label: "Inne", icon: MessageSquare }
  ];

  if (variant === "compact") {
    return (
      <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="text-xl text-white flex items-center gap-2">
            <Phone className="h-5 w-5 text-green-400" />
            Szybki Kontakt
          </CardTitle>
          <CardDescription className="text-slate-300">
            Zadzwoń lub zostaw wiadomość
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
            <Phone className="h-5 w-5 text-green-400" />
            <div>
              <div className="text-white font-medium">{contactInfo.phone}</div>
              <div className="text-sm text-slate-400">Pon-Pt: 8:00-18:00</div>
            </div>
          </div>
          
          <Form method="post" action="/api/leads/create" className="space-y-3">
            <input type="hidden" name="source" value={source} />
            <input type="hidden" name="service" value={selectedService} />
            
            <div className="grid grid-cols-2 gap-3">
              <Input
                name="name"
                placeholder="Imię"
                required
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
              <Input
                name="phone"
                placeholder="Telefon"
                required
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
            
            <Textarea
              name="message"
              placeholder="Krótka wiadomość..."
              rows={2}
              className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
            />
            
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
            >
              {isSubmitting ? (
                "Wysyłanie..."
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Wyślij
                </>
              )}
            </Button>
          </Form>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`bg-slate-800/50 backdrop-blur-sm border-slate-700 text-white ${className}`}>
      <CardHeader>
        <CardTitle className="text-2xl font-bold flex items-center gap-3">
          <Mail className="h-6 w-6 text-blue-400" />
          Skontaktuj się z nami
        </CardTitle>
        <CardDescription className="text-slate-300 text-lg">
          Bezpłatna wycena i doradztwo techniczne. Odpowiemy w ciągu 2 godzin.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Contact Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="flex items-center gap-3 p-4 bg-slate-700/30 rounded-lg">
            <Phone className="h-5 w-5 text-green-400" />
            <div>
              <div className="font-medium">{contactInfo.phone}</div>
              <div className="text-sm text-slate-400">Pon-Pt: 8:00-18:00</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-slate-700/30 rounded-lg">
            <Mail className="h-5 w-5 text-blue-400" />
            <div>
              <div className="font-medium">{contactInfo.email}</div>
              <div className="text-sm text-slate-400">24/7 online</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-4 bg-slate-700/30 rounded-lg">
            <MapPin className="h-5 w-5 text-purple-400" />
            <div>
              <div className="font-medium">Warszawa</div>
              <div className="text-sm text-slate-400">i okolice</div>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {actionData?.success && (
          <div className="flex items-center gap-3 p-4 bg-green-500/20 border border-green-500/30 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div>
              <div className="font-medium text-green-400">Wiadomość wysłana!</div>
              <div className="text-sm text-green-300">Skontaktujemy się w ciągu 2 godzin.</div>
            </div>
          </div>
        )}

        {actionData?.error && (
          <div className="flex items-center gap-3 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div>
              <div className="font-medium text-red-400">Błąd wysyłania</div>
              <div className="text-sm text-red-300">{actionData.error}</div>
            </div>
          </div>
        )}

        {/* Contact Form */}
        <Form method="post" action="/api/leads/create" className="space-y-6">
          <input type="hidden" name="source" value={source} />
          
          {/* Service Selection */}
          <div className="space-y-2">
            <Label htmlFor="service" className="text-white">Rodzaj usługi</Label>
            <Select name="service" value={selectedService} onValueChange={setSelectedService}>
              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                <SelectValue placeholder="Wybierz usługę" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                {services.map((service) => {
                  const Icon = service.icon;
                  return (
                    <SelectItem key={service.value} value={service.value} className="text-white">
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {service.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="text-white">Imię i nazwisko *</Label>
              <Input
                id="name"
                name="name"
                required
                placeholder="Jan Kowalski"
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-white">Telefon *</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                required
                placeholder="+48 600 100 901"
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="location" className="text-white">Lokalizacja</Label>
              <Input
                id="location"
                name="location"
                placeholder="Warszawa, Mokotów"
                className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
              />
            </div>
          </div>

          {/* Message */}
          <div className="space-y-2">
            <Label htmlFor="message" className="text-white">Wiadomość</Label>
            <Textarea
              id="message"
              name="message"
              placeholder="Opisz swoje potrzeby, wielkość pomieszczenia, preferowaną markę..."
              rows={4}
              className="bg-slate-700/50 border-slate-600 text-white placeholder-slate-400"
            />
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            disabled={isSubmitting}
            className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white font-semibold py-3 transition-all duration-300 hover:scale-105"
          >
            {isSubmitting ? (
              "Wysyłanie..."
            ) : (
              <>
                <Send className="h-5 w-5 mr-2" />
                Wyślij zapytanie
              </>
            )}
          </Button>
          
          <p className="text-xs text-slate-400 text-center">
            Wysyłając formularz akceptujesz naszą politykę prywatności. 
            Odpowiemy w ciągu 2 godzin w dni robocze.
          </p>
        </Form>
      </CardContent>
    </Card>
  );
}
