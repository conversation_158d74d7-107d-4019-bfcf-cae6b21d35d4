/**
 * Enhanced Communication Hub Component
 * Unified interface for all communication channels
 * Part of FAZA 1: Komunikacja & Przetwarzanie Danych
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '~/components/ui/tabs';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { 
  Mail, 
  Phone, 
  MessageSquare, 
  Send, 
  Filter, 
  Search,
  TrendingUp,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';

interface UnifiedMessage {
  id: string;
  customerId: string;
  channel: string;
  direction: 'INBOUND' | 'OUTBOUND';
  subject?: string;
  content: string;
  timestamp: Date;
  read: boolean;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  sentiment: 'POSITIVE' | 'NEUTRAL' | 'NEGATIVE';
  category: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
}

interface CommunicationInsights {
  totalMessages: number;
  unreadCount: number;
  channelBreakdown: Record<string, number>;
  sentimentAnalysis: {
    positive: number;
    neutral: number;
    negative: number;
  };
  priorityBreakdown: Record<string, number>;
  responseTimeAvg: number;
  topCategories: Array<{ category: string; count: number }>;
}

interface CommunicationHubProps {
  customerId?: string;
  initialMessages?: UnifiedMessage[];
  insights?: CommunicationInsights;
}

export function CommunicationHub({ 
  customerId, 
  initialMessages = [], 
  insights 
}: CommunicationHubProps) {
  const [messages, setMessages] = useState<UnifiedMessage[]>(initialMessages);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('messages');
  const [filters, setFilters] = useState({
    channel: '',
    unreadOnly: false,
    priority: '',
    sentiment: '',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // Load messages
  const loadMessages = async () => {
    if (!customerId) return;
    
    setLoading(true);
    try {
      const params = new URLSearchParams({
        action: 'messages',
        customerId,
        limit: '50',
        offset: '0',
      });

      if (filters.channel) params.append('channels', filters.channel);
      if (filters.unreadOnly) params.append('unreadOnly', 'true');

      const response = await fetch(`/api/communication-hub?${params}`);
      const data = await response.json();
      
      if (data.messages) {
        setMessages(data.messages);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMessages();
  }, [customerId, filters]);

  // Filter messages based on search and filters
  const filteredMessages = messages.filter(message => {
    if (searchTerm && !message.content.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !message.subject?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    if (filters.priority && message.priority !== filters.priority) {
      return false;
    }
    
    if (filters.sentiment && message.sentiment !== filters.sentiment) {
      return false;
    }
    
    return true;
  });

  // Mark messages as read
  const markAsRead = async (messageIds: string[]) => {
    try {
      const formData = new FormData();
      formData.append('action', 'mark-messages-read');
      formData.append('messageIds', JSON.stringify(messageIds));

      await fetch('/api/communication-hub', {
        method: 'POST',
        body: formData,
      });

      setMessages(prev => 
        prev.map(msg => 
          messageIds.includes(msg.id) ? { ...msg, read: true } : msg
        )
      );
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  // Get channel icon
  const getChannelIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'phone': return <Phone className="h-4 w-4" />;
      case 'sms': return <MessageSquare className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT': return 'destructive';
      case 'HIGH': return 'secondary';
      case 'MEDIUM': return 'outline';
      case 'LOW': return 'outline';
      default: return 'outline';
    }
  };

  // Get sentiment color
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'POSITIVE': return 'bg-green-100 text-green-800';
      case 'NEGATIVE': return 'bg-red-100 text-red-800';
      case 'NEUTRAL': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with insights */}
      {insights && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium">Total Messages</p>
                  <p className="text-2xl font-bold">{insights.totalMessages}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm font-medium">Unread</p>
                  <p className="text-2xl font-bold">{insights.unreadCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium">Avg Response</p>
                  <p className="text-2xl font-bold">{Math.round(insights.responseTimeAvg)}h</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="text-sm font-medium">Positive Sentiment</p>
                  <p className="text-2xl font-bold">
                    {Math.round((insights.sentimentAnalysis.positive / insights.totalMessages) * 100)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main communication interface */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5" />
              <span>Communication Hub</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadMessages()}
                disabled={loading}
              >
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="messages">Messages</TabsTrigger>
              <TabsTrigger value="compose">Compose</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="messages" className="space-y-4">
              {/* Filters and search */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <select
                    value={filters.channel}
                    onChange={(e) => setFilters(prev => ({ ...prev, channel: e.target.value }))}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="">All Channels</option>
                    <option value="EMAIL">Email</option>
                    <option value="PHONE">Phone</option>
                    <option value="SMS">SMS</option>
                  </select>

                  <select
                    value={filters.priority}
                    onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="">All Priorities</option>
                    <option value="URGENT">Urgent</option>
                    <option value="HIGH">High</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="LOW">Low</option>
                  </select>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setFilters(prev => ({ ...prev, unreadOnly: !prev.unreadOnly }))}
                    className={filters.unreadOnly ? 'bg-blue-50' : ''}
                  >
                    {filters.unreadOnly ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    {filters.unreadOnly ? 'Show All' : 'Unread Only'}
                  </Button>
                </div>
              </div>

              {/* Messages list */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-4 border rounded-lg hover:bg-gray-50 cursor-pointer ${
                      !message.read ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => !message.read && markAsRead([message.id])}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="flex items-center space-x-2">
                          {getChannelIcon(message.channel)}
                          <Badge variant={getPriorityColor(message.priority)}>
                            {message.priority}
                          </Badge>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <p className="font-medium text-sm">{message.customer.name}</p>
                            <span className={`px-2 py-1 rounded-full text-xs ${getSentimentColor(message.sentiment)}`}>
                              {message.sentiment}
                            </span>
                            {!message.read && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                          
                          {message.subject && (
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {message.subject}
                            </p>
                          )}
                          
                          <p className="text-sm text-gray-600 truncate">
                            {message.content}
                          </p>
                          
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{new Date(message.timestamp).toLocaleString()}</span>
                            <span>{message.category}</span>
                            <span className={`px-2 py-1 rounded ${
                              message.direction === 'INBOUND' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {message.direction}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {filteredMessages.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No messages found
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="compose" className="space-y-4">
              <ComposeMessage customerId={customerId} onSent={loadMessages} />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <CommunicationAnalytics insights={insights} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// Compose Message Component
function ComposeMessage({ customerId, onSent }: { customerId?: string; onSent: () => void }) {
  const [channels, setChannels] = useState<string[]>(['EMAIL']);
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);

  const handleSend = async () => {
    if (!customerId || !message.trim()) return;

    setSending(true);
    try {
      const formData = new FormData();
      formData.append('action', 'send-unified-message');
      formData.append('customerId', customerId);
      formData.append('channels', JSON.stringify(channels));
      formData.append('content', JSON.stringify({
        subject: subject || undefined,
        message: message.trim(),
      }));

      await fetch('/api/communication-hub', {
        method: 'POST',
        body: formData,
      });

      setSubject('');
      setMessage('');
      onSent();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">Channels</label>
        <div className="flex gap-2">
          {['EMAIL', 'SMS', 'PHONE'].map(channel => (
            <Button
              key={channel}
              variant={channels.includes(channel) ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                setChannels(prev => 
                  prev.includes(channel) 
                    ? prev.filter(c => c !== channel)
                    : [...prev, channel]
                );
              }}
            >
              {getChannelIcon(channel)}
              {channel}
            </Button>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Subject (optional)</label>
        <Input
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          placeholder="Message subject..."
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Message</label>
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your message..."
          rows={6}
        />
      </div>

      <Button
        onClick={handleSend}
        disabled={!customerId || !message.trim() || sending || channels.length === 0}
        className="w-full"
      >
        <Send className="h-4 w-4 mr-2" />
        {sending ? 'Sending...' : 'Send Message'}
      </Button>
    </div>
  );
}

// Communication Analytics Component
function CommunicationAnalytics({ insights }: { insights?: CommunicationInsights }) {
  if (!insights) {
    return <div className="text-center py-8 text-gray-500">No analytics data available</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Channel Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Object.entries(insights.channelBreakdown).map(([channel, count]) => (
              <div key={channel} className="flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  {getChannelIcon(channel)}
                  <span>{channel}</span>
                </div>
                <Badge variant="outline">{count}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Top Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {insights.topCategories.map(({ category, count }) => (
              <div key={category} className="flex justify-between items-center">
                <span>{category}</span>
                <Badge variant="outline">{count}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function getChannelIcon(channel: string) {
  switch (channel.toLowerCase()) {
    case 'email': return <Mail className="h-4 w-4" />;
    case 'phone': return <Phone className="h-4 w-4" />;
    case 'sms': return <MessageSquare className="h-4 w-4" />;
    default: return <MessageSquare className="h-4 w-4" />;
  }
}
