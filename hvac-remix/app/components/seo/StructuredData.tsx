import { fulmarkData, contactInfo } from "~/data/fulmark-data";

interface StructuredDataProps {
  type: "organization" | "service" | "localBusiness" | "product" | "article";
  data?: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseUrl = "https://fulmark.pl"; // Replace with actual domain
    
    switch (type) {
      case "organization":
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": fulmarkData.company.fullName,
          "alternateName": fulmarkData.company.name,
          "description": fulmarkData.company.description,
          "url": baseUrl,
          "logo": `${baseUrl}/images/fulmark-logo.png`,
          "image": `${baseUrl}/images/fulmark-hero.jpg`,
          "telephone": contactInfo.phone,
          "email": contactInfo.email,
          "address": {
            "@type": "PostalAddress",
            "addressLocality": "Warszawa",
            "addressRegion": "Mazowieckie",
            "addressCountry": "PL"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": 52.2297,
            "longitude": 21.0122
          },
          "openingHours": [
            "Mo-Fr 08:00-18:00",
            "Sa 09:00-15:00"
          ],
          "priceRange": "$$",
          "paymentAccepted": ["Cash", "Credit Card", "Bank Transfer"],
          "currenciesAccepted": "PLN",
          "areaServed": [
            {
              "@type": "City",
              "name": "Warszawa"
            },
            {
              "@type": "City", 
              "name": "Piaseczno"
            }
          ],
          "serviceType": [
            "Montaż klimatyzacji",
            "Serwis klimatyzacji",
            "Naprawa klimatyzacji"
          ],
          "brand": [
            {
              "@type": "Brand",
              "name": "LG"
            },
            {
              "@type": "Brand",
              "name": "Daikin"
            }
          ],
          "foundingDate": "2000",
          "numberOfEmployees": "5-10",
          "slogan": fulmarkData.company.tagline
        };

      case "localBusiness":
        return {
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "@id": `${baseUrl}#business`,
          "name": fulmarkData.company.fullName,
          "description": fulmarkData.company.description,
          "url": baseUrl,
          "telephone": contactInfo.phone,
          "email": contactInfo.email,
          "image": `${baseUrl}/images/fulmark-business.jpg`,
          "address": {
            "@type": "PostalAddress",
            "addressLocality": "Warszawa",
            "addressRegion": "Mazowieckie", 
            "postalCode": "00-000",
            "addressCountry": "PL"
          },
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": 52.2297,
            "longitude": 21.0122
          },
          "openingHoursSpecification": [
            {
              "@type": "OpeningHoursSpecification",
              "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
              "opens": "08:00",
              "closes": "18:00"
            },
            {
              "@type": "OpeningHoursSpecification", 
              "dayOfWeek": "Saturday",
              "opens": "09:00",
              "closes": "15:00"
            }
          ],
          "priceRange": "$$",
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "150",
            "bestRating": "5",
            "worstRating": "1"
          },
          "review": [
            {
              "@type": "Review",
              "author": {
                "@type": "Person",
                "name": "Anna Kowalska"
              },
              "reviewRating": {
                "@type": "Rating",
                "ratingValue": "5",
                "bestRating": "5"
              },
              "reviewBody": "Profesjonalny montaż klimatyzacji LG. Szybko, sprawnie i w dobrej cenie. Polecam!"
            }
          ]
        };

      case "service":
        return fulmarkData.services.map(service => ({
          "@context": "https://schema.org",
          "@type": "Service",
          "name": service.name,
          "description": service.description,
          "provider": {
            "@type": "Organization",
            "name": fulmarkData.company.fullName,
            "telephone": contactInfo.phone,
            "email": contactInfo.email
          },
          "areaServed": fulmarkData.serviceAreas.map(area => ({
            "@type": "City",
            "name": area.name
          })),
          "serviceType": "HVAC Service",
          "category": "Air Conditioning",
          "offers": {
            "@type": "Offer",
            "availability": "https://schema.org/InStock",
            "price": service.id === "wycena" ? "0" : "200",
            "priceCurrency": "PLN",
            "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            "seller": {
              "@type": "Organization",
              "name": fulmarkData.company.fullName
            }
          }
        }));

      case "product":
        return fulmarkData.productCategories.map(product => ({
          "@context": "https://schema.org",
          "@type": "Product",
          "name": product.name,
          "description": product.description,
          "brand": product.brands.map(brand => ({
            "@type": "Brand",
            "name": brand
          })),
          "category": "Air Conditioning Equipment",
          "offers": {
            "@type": "AggregateOffer",
            "lowPrice": product.priceRange.split('-')[0].replace(/[^\d]/g, ''),
            "highPrice": product.priceRange.split('-')[1]?.replace(/[^\d]/g, '') || "50000",
            "priceCurrency": "PLN",
            "availability": "https://schema.org/InStock",
            "seller": {
              "@type": "Organization",
              "name": fulmarkData.company.fullName
            }
          },
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.7",
            "reviewCount": "85",
            "bestRating": "5",
            "worstRating": "1"
          }
        }));

      case "article":
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": data?.title || "Profesjonalna klimatyzacja w Warszawie",
          "description": data?.description || fulmarkData.company.description,
          "image": data?.image || `${baseUrl}/images/article-default.jpg`,
          "author": {
            "@type": "Organization",
            "name": fulmarkData.company.fullName
          },
          "publisher": {
            "@type": "Organization",
            "name": fulmarkData.company.fullName,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/fulmark-logo.png`
            }
          },
          "datePublished": data?.publishedDate || new Date().toISOString(),
          "dateModified": data?.modifiedDate || new Date().toISOString(),
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data?.url || baseUrl
          }
        };

      default:
        return null;
    }
  };

  const structuredData = getStructuredData();
  
  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(Array.isArray(structuredData) ? structuredData : [structuredData], null, 2)
      }}
    />
  );
}
