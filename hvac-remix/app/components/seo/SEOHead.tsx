import { fulmarkData, contactInfo } from "~/data/fulmark-data";

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: "website" | "article" | "service" | "product";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  noIndex?: boolean;
  canonical?: string;
}

export function generateSEOMeta({
  title,
  description,
  keywords,
  image,
  url,
  type = "website",
  publishedTime,
  modifiedTime,
  author,
  noIndex = false,
  canonical
}: SEOHeadProps = {}) {
  const baseUrl = "https://fulmark.pl"; // Replace with actual domain
  const defaultTitle = `${fulmarkData.company.name} - ${fulmarkData.company.tagline}`;
  const defaultDescription = fulmarkData.company.description;
  const defaultImage = `${baseUrl}/images/fulmark-og-image.jpg`;
  const defaultKeywords = "klimatyzacja warszawa, montaż klimatyzacji, ser<PERSON><PERSON> klimat<PERSON>zacji, lg daikin, fulmark";

  const finalTitle = title ? `${title} | ${fulmarkData.company.name}` : defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalUrl = url || baseUrl;
  const finalKeywords = keywords || defaultKeywords;

  const meta = [
    // Basic Meta Tags
    { title: finalTitle },
    { name: "description", content: finalDescription },
    { name: "keywords", content: finalKeywords },
    { name: "author", content: author || fulmarkData.company.fullName },
    { name: "robots", content: noIndex ? "noindex, nofollow" : "index, follow" },
    { name: "viewport", content: "width=device-width, initial-scale=1" },
    { name: "theme-color", content: "#1e293b" },
    
    // Canonical URL
    ...(canonical ? [{ tagName: "link", rel: "canonical", href: canonical }] : []),

    // Open Graph Tags
    { property: "og:title", content: finalTitle },
    { property: "og:description", content: finalDescription },
    { property: "og:image", content: finalImage },
    { property: "og:url", content: finalUrl },
    { property: "og:type", content: type },
    { property: "og:site_name", content: fulmarkData.company.name },
    { property: "og:locale", content: "pl_PL" },
    
    // Twitter Card Tags
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: finalTitle },
    { name: "twitter:description", content: finalDescription },
    { name: "twitter:image", content: finalImage },
    
    // Business Information
    { name: "business:contact_data:street_address", content: "Warszawa" },
    { name: "business:contact_data:locality", content: "Warszawa" },
    { name: "business:contact_data:region", content: "Mazowieckie" },
    { name: "business:contact_data:postal_code", content: "00-000" },
    { name: "business:contact_data:country_name", content: "Polska" },
    { name: "business:contact_data:phone_number", content: contactInfo.phone },
    { name: "business:contact_data:email", content: contactInfo.email },
    
    // Article specific tags
    ...(type === "article" && publishedTime ? [
      { property: "article:published_time", content: publishedTime },
      { property: "article:modified_time", content: modifiedTime || publishedTime },
      { property: "article:author", content: author || fulmarkData.company.fullName },
      { property: "article:section", content: "HVAC" },
      { property: "article:tag", content: "klimatyzacja" },
      { property: "article:tag", content: "warszawa" },
    ] : []),

    // Local Business Schema
    { name: "geo.region", content: "PL-MZ" },
    { name: "geo.placename", content: "Warszawa" },
    { name: "geo.position", content: "52.2297;21.0122" },
    { name: "ICBM", content: "52.2297, 21.0122" },

    // Mobile App Tags
    { name: "apple-mobile-web-app-capable", content: "yes" },
    { name: "apple-mobile-web-app-status-bar-style", content: "black-translucent" },
    { name: "apple-mobile-web-app-title", content: fulmarkData.company.name },
    { name: "mobile-web-app-capable", content: "yes" },
    { name: "application-name", content: fulmarkData.company.name },

    // Security Headers
    { "http-equiv": "X-Content-Type-Options", content: "nosniff" },
    { "http-equiv": "X-Frame-Options", content: "DENY" },
    { "http-equiv": "X-XSS-Protection", content: "1; mode=block" },

    // Performance Hints
    { name: "dns-prefetch", content: "//fonts.googleapis.com" },
    { name: "dns-prefetch", content: "//www.google-analytics.com" },
    { name: "preconnect", content: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  ];

  return meta.filter(Boolean);
}

// Predefined SEO configurations for common pages
export const seoConfigs = {
  home: {
    title: "Profesjonalna klimatyzacja w Warszawie",
    description: `${fulmarkData.company.fullName} - ${fulmarkData.company.experience} doświadczenia w montażu i serwisie klimatyzacji LG i Daikin w Warszawie i okolicach. Bezpłatna wycena!`,
    keywords: "klimatyzacja warszawa, montaż klimatyzacji, serwis klimatyzacji, lg daikin, fulmark, piaseczno, mokotów, ursynów"
  },
  
  contact: {
    title: "Kontakt - Fulmark Klimatyzacja",
    description: `Skontaktuj się z ${fulmarkData.company.name}. Tel: ${contactInfo.phone}. Bezpłatna wycena i doradztwo techniczne. Warszawa i okolice.`,
    keywords: "kontakt fulmark, klimatyzacja warszawa telefon, montaż klimatyzacji kontakt, serwis klimatyzacji warszawa"
  },
  
  booking: {
    title: "Umów wizytę online - Rezerwacja",
    description: "Zarezerwuj wizytę online z Fulmark. Montaż, serwis i naprawa klimatyzacji w Warszawie. Wybierz termin i zostaw dane - potwierdzimy telefonicznie.",
    keywords: "rezerwacja wizyty klimatyzacja, umów wizytę montaż, serwis klimatyzacji online, fulmark rezerwacja"
  },
  
  calculator: {
    title: "Kalkulator kosztów klimatyzacji",
    description: "Sprawdź orientacyjny koszt montażu klimatyzacji. Kalkulator uwzględnia typ budynku, powierzchnię, markę LG/Daikin i poziom skomplikowania.",
    keywords: "kalkulator kosztów klimatyzacji, cena montażu klimatyzacji, koszt klimatyzacji warszawa, lg daikin cena"
  },
  
  services: {
    title: "Usługi klimatyzacyjne - Montaż, Serwis, Naprawa",
    description: "Kompleksowe usługi klimatyzacyjne: montaż, serwis, naprawa klimatyzacji LG i Daikin. Autoryzowany serwis w Warszawie i okolicach.",
    keywords: "usługi klimatyzacyjne, montaż klimatyzacji lg daikin, serwis klimatyzacji warszawa, naprawa klimatyzacji"
  },
  
  areas: {
    title: "Obszar działania - Warszawa i okolice",
    description: "Obsługujemy Warszawę i okolice: Mokotów, Ursynów, Piaseczno, Wilanów. Szybki dojazd i profesjonalna obsługa klimatyzacji.",
    keywords: "klimatyzacja warszawa mokotów, klimatyzacja ursynów, klimatyzacja piaseczno, obszar działania fulmark"
  }
};

// Helper function to get SEO config by page type
export function getSEOConfig(pageType: keyof typeof seoConfigs, customData?: Partial<SEOHeadProps>) {
  const baseConfig = seoConfigs[pageType];
  return generateSEOMeta({ ...baseConfig, ...customData });
}
